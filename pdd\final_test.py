import sys
import os
sys.path.insert(0, '.')

print("开始测试...")

try:
    import license_system.crypto_utils as crypto
    print("✅ 导入成功")
    
    # 测试生成卡密
    license_key = crypto.LicenseGenerator.generate_license(
        user_id="TEST001",
        license_type="personal", 
        duration_days=30,
        features=["basic"],
        hardware_fingerprint=None
    )
    
    print(f"✅ 生成卡密: {license_key}")
    
    # 测试解密
    decoded = crypto.CryptoUtils.decode_license_data(license_key)
    print(f"✅ 解密成功，用户ID: {decoded['user_id']}")
    
    print("🎉 加密功能正常工作！")
    
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()

print("测试完成")
