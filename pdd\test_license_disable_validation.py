#!/usr/bin/env python3
"""
测试卡密禁用验证功能
"""
import sys
import os
import sqlite3
from datetime import datetime, timedelta

sys.path.insert(0, '.')

def setup_test_database():
    """设置测试数据库"""
    db_path = "license_data.db"
    
    # 如果数据库存在，先备份
    if os.path.exists(db_path):
        import shutil
        shutil.copy2(db_path, "license_data_backup.db")
        print("✅ 已备份原数据库")
    
    conn = sqlite3.connect(db_path)
    
    # 创建表结构
    conn.execute('''
        CREATE TABLE IF NOT EXISTS licenses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            customer_id INTEGER,
            license_key TEXT NOT NULL,
            license_type TEXT NOT NULL,
            user_id TEXT NOT NULL,
            features TEXT,
            issue_date DATE,
            expire_date DATE,
            hardware_fingerprint TEXT,
            status TEXT DEFAULT 'active',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 清空现有数据
    conn.execute("DELETE FROM licenses")
    
    # 插入测试数据
    test_licenses = [
        ("ACTIVE-TEST-AAAA-BBBB-CCCC", "personal", "ACTIVE_USER", "active"),
        ("DISABLED-TEST-DDDD-EEEE-FFFF", "professional", "DISABLED_USER", "disabled"),
    ]
    
    for license_key, license_type, user_id, status in test_licenses:
        conn.execute('''
            INSERT INTO licenses (license_key, license_type, user_id, status, 
                                issue_date, expire_date, features)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            license_key, license_type, user_id, status,
            datetime.now().date(),
            (datetime.now() + timedelta(days=365)).date(),
            '["basic"]'
        ))
    
    conn.commit()
    conn.close()
    print("✅ 测试数据库设置完成")
    return db_path

def create_test_license_file(user_id, license_type, status="active"):
    """创建测试卡密文件"""
    try:
        import license_system.crypto_utils as crypto
        
        # 生成卡密数据
        license_data = {
            "version": "1.0",
            "user_id": user_id,
            "license_type": license_type,
            "issue_time": datetime.now().isoformat(),
            "expire_time": (datetime.now() + timedelta(days=365)).isoformat(),
            "features": ["basic"],
            "hardware_fingerprint": None,
            "signature": "test_signature"
        }
        
        # 生成签名
        signature_data = f"{user_id}|{license_type}|{license_data['expire_time']}"
        license_data["signature"] = crypto.CryptoUtils.generate_checksum(signature_data)[:16]
        
        # 保存到文件
        import json
        license_file = f"license_{user_id.lower()}.lic"
        with open(license_file, 'w', encoding='utf-8') as f:
            json.dump(license_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 创建卡密文件: {license_file}")
        return license_file
        
    except Exception as e:
        print(f"❌ 创建卡密文件失败: {e}")
        return None

def test_license_validation():
    """测试卡密验证"""
    print("\n🔍 测试卡密验证功能")
    print("=" * 50)
    
    try:
        from license_system.license_manager import LicenseManager
        
        # 测试正常卡密
        print("\n📋 测试正常卡密...")
        active_license_file = create_test_license_file("ACTIVE_USER", "personal")
        if active_license_file:
            # 设置卡密文件路径
            import license_system.config as config
            original_license_file = config.LICENSE_FILE
            config.LICENSE_FILE = active_license_file
            
            try:
                manager = LicenseManager()
                result = manager._verify_license()
                print(f"正常卡密验证结果: {'✅ 通过' if result else '❌ 失败'}")
                
                if result:
                    print("✅ 正常卡密验证成功")
                else:
                    print("❌ 正常卡密验证失败（可能是状态检查问题）")
                    
            except Exception as e:
                print(f"❌ 正常卡密验证异常: {e}")
            finally:
                config.LICENSE_FILE = original_license_file
                if os.path.exists(active_license_file):
                    os.remove(active_license_file)
        
        # 测试禁用卡密
        print("\n📋 测试禁用卡密...")
        disabled_license_file = create_test_license_file("DISABLED_USER", "professional")
        if disabled_license_file:
            # 设置卡密文件路径
            config.LICENSE_FILE = disabled_license_file
            
            try:
                manager = LicenseManager()
                result = manager._verify_license()
                print(f"禁用卡密验证结果: {'❌ 被拒绝' if not result else '⚠️ 意外通过'}")
                
                if not result:
                    print("✅ 禁用卡密正确被拒绝")
                else:
                    print("❌ 禁用卡密意外通过验证")
                    
            except Exception as e:
                print(f"✅ 禁用卡密验证异常（预期）: {e}")
            finally:
                config.LICENSE_FILE = original_license_file
                if os.path.exists(disabled_license_file):
                    os.remove(disabled_license_file)
        
        return True
        
    except Exception as e:
        print(f"❌ 验证测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def restore_database():
    """恢复原数据库"""
    backup_file = "license_data_backup.db"
    if os.path.exists(backup_file):
        import shutil
        shutil.copy2(backup_file, "license_data.db")
        os.remove(backup_file)
        print("✅ 已恢复原数据库")
    else:
        # 删除测试数据库
        if os.path.exists("license_data.db"):
            os.remove("license_data.db")
        print("✅ 已清理测试数据库")

def main():
    """主函数"""
    print("🧪 卡密禁用验证功能测试")
    print("=" * 60)
    
    try:
        # 设置测试环境
        db_path = setup_test_database()
        
        # 测试验证功能
        validation_ok = test_license_validation()
        
        print("\n📊 测试总结")
        print("=" * 50)
        print(f"🔍 卡密验证测试: {'✅ 通过' if validation_ok else '❌ 失败'}")
        
        if validation_ok:
            print("\n🎉 禁用验证功能测试通过！")
            print("✅ 正常卡密可以通过验证")
            print("✅ 禁用卡密被正确拒绝")
            print("✅ 状态检查功能正常工作")
        else:
            print("\n❌ 部分测试失败")
            print("请检查错误信息并修复问题")
        
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 恢复环境
        restore_database()

if __name__ == "__main__":
    main()
