#!/usr/bin/env python3
"""
卡密生成器主程序
用于生成和管理拼多多采集工具的卡密
"""
import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import sqlite3
from datetime import datetime, timedelta
from pathlib import Path

# 添加父目录到路径，以便导入license_system
sys.path.insert(0, str(Path(__file__).parent.parent))

try:
    from license_system.crypto_utils import LicenseGenerator, CryptoUtils
    from license_system.config import LICENSE_TYPES, FEATURES
    from license_system.hardware_info import HardwareInfo
except ImportError as e:
    print(f"错误：无法导入卡密系统模块: {e}")
    print("请确保license_system目录存在且完整")
    sys.exit(1)


class LicenseGeneratorGUI:
    """卡密生成器GUI"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔑 拼多多采集工具 - 卡密生成器")
        self.root.geometry("900x700")
        self.root.resizable(True, True)
        
        # 初始化数据库
        self.init_database()
        
        # 创建界面
        self.create_widgets()
        
        # 加载数据
        self.refresh_customer_list()
        
    def init_database(self):
        """初始化数据库"""
        self.db_path = Path(__file__).parent / "license_data.db"
        self.conn = sqlite3.connect(self.db_path)
        
        # 创建表
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                contact TEXT,
                email TEXT,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS licenses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_id INTEGER,
                license_key TEXT NOT NULL,
                license_type TEXT NOT NULL,
                user_id TEXT NOT NULL,
                features TEXT NOT NULL,
                issue_date DATE NOT NULL,
                expire_date DATE NOT NULL,
                hardware_fingerprint TEXT,
                status TEXT DEFAULT 'active',
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers (id)
            )
        ''')
        
        self.conn.commit()
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建笔记本控件
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 卡密生成标签页
        self.create_generate_tab(notebook)
        
        # 客户管理标签页
        self.create_customer_tab(notebook)
        
        # 卡密管理标签页
        self.create_license_tab(notebook)
        
        # 工具标签页
        self.create_tools_tab(notebook)
    
    def create_generate_tab(self, notebook):
        """创建卡密生成标签页"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="🔑 生成卡密")
        
        # 主框架
        main_frame = ttk.Frame(frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 客户选择
        customer_frame = ttk.LabelFrame(main_frame, text="客户信息", padding=10)
        customer_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(customer_frame, text="客户:").grid(row=0, column=0, sticky=tk.W, padx=5)
        self.customer_var = tk.StringVar()
        self.customer_combo = ttk.Combobox(customer_frame, textvariable=self.customer_var, 
                                          width=30, state="readonly")
        self.customer_combo.grid(row=0, column=1, padx=5, sticky=tk.W)
        
        ttk.Button(customer_frame, text="新建客户", 
                  command=self.show_new_customer_dialog).grid(row=0, column=2, padx=10)
        
        # 卡密配置
        config_frame = ttk.LabelFrame(main_frame, text="卡密配置", padding=10)
        config_frame.pack(fill=tk.X, pady=10)
        
        # 授权类型
        ttk.Label(config_frame, text="授权类型:").grid(row=0, column=0, sticky=tk.W, padx=5)
        self.license_type_var = tk.StringVar(value="personal")
        license_type_combo = ttk.Combobox(config_frame, textvariable=self.license_type_var,
                                         values=list(LICENSE_TYPES.keys()), state="readonly")
        license_type_combo.grid(row=0, column=1, padx=5, sticky=tk.W)
        license_type_combo.bind('<<ComboboxSelected>>', self.on_license_type_change)
        
        # 有效期
        ttk.Label(config_frame, text="有效期(天):").grid(row=1, column=0, sticky=tk.W, padx=5)
        self.duration_var = tk.StringVar(value="365")
        duration_entry = ttk.Entry(config_frame, textvariable=self.duration_var, width=10)
        duration_entry.grid(row=1, column=1, padx=5, sticky=tk.W)
        
        # 用户ID
        ttk.Label(config_frame, text="用户ID:").grid(row=2, column=0, sticky=tk.W, padx=5)
        self.user_id_var = tk.StringVar()
        user_id_entry = ttk.Entry(config_frame, textvariable=self.user_id_var, width=20)
        user_id_entry.grid(row=2, column=1, padx=5, sticky=tk.W)
        
        ttk.Button(config_frame, text="生成ID", 
                  command=self.generate_user_id).grid(row=2, column=2, padx=10)
        
        # 功能权限
        features_frame = ttk.LabelFrame(main_frame, text="功能权限", padding=10)
        features_frame.pack(fill=tk.X, pady=10)
        
        self.feature_vars = {}
        row = 0
        col = 0
        for feature_key, feature_name in FEATURES.items():
            var = tk.BooleanVar()
            self.feature_vars[feature_key] = var
            cb = ttk.Checkbutton(features_frame, text=f"{feature_name} ({feature_key})", 
                               variable=var)
            cb.grid(row=row, column=col, sticky=tk.W, padx=10, pady=2)
            col += 1
            if col > 1:
                col = 0
                row += 1
        
        # 硬件绑定
        hardware_frame = ttk.LabelFrame(main_frame, text="硬件绑定", padding=10)
        hardware_frame.pack(fill=tk.X, pady=10)
        
        self.hardware_binding_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(hardware_frame, text="启用硬件绑定", 
                       variable=self.hardware_binding_var).pack(anchor=tk.W)
        
        ttk.Label(hardware_frame, text="硬件指纹:").pack(anchor=tk.W, pady=(10,0))
        self.hardware_fp_var = tk.StringVar()
        hardware_entry = ttk.Entry(hardware_frame, textvariable=self.hardware_fp_var, width=50)
        hardware_entry.pack(fill=tk.X, pady=5)
        
        ttk.Button(hardware_frame, text="获取当前设备指纹", 
                  command=self.get_current_hardware).pack(anchor=tk.W)
        
        # 生成按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=20)
        
        ttk.Button(button_frame, text="🔑 生成卡密", 
                  command=self.generate_license).pack(side=tk.LEFT, padx=10)
        
        ttk.Button(button_frame, text="📋 批量生成", 
                  command=self.batch_generate).pack(side=tk.LEFT, padx=10)
        
        # 结果显示
        result_frame = ttk.LabelFrame(main_frame, text="生成结果", padding=10)
        result_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        self.result_text = tk.Text(result_frame, height=8, wrap=tk.WORD)
        result_scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, 
                                        command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=result_scrollbar.set)
        
        self.result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        result_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 初始化授权类型
        self.on_license_type_change()
    
    def create_customer_tab(self, notebook):
        """创建客户管理标签页"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="👥 客户管理")
        
        # 客户列表
        list_frame = ttk.LabelFrame(frame, text="客户列表", padding=10)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 创建表格
        columns = ("ID", "姓名", "联系方式", "邮箱", "创建时间")
        self.customer_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)
        
        for col in columns:
            self.customer_tree.heading(col, text=col)
            self.customer_tree.column(col, width=120)
        
        customer_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, 
                                          command=self.customer_tree.yview)
        self.customer_tree.configure(yscrollcommand=customer_scrollbar.set)
        
        self.customer_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        customer_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 按钮
        button_frame = ttk.Frame(frame)
        button_frame.pack(fill=tk.X, padx=20, pady=10)
        
        ttk.Button(button_frame, text="➕ 新建客户", 
                  command=self.show_new_customer_dialog).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="✏️ 编辑客户", 
                  command=self.edit_customer).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🗑️ 删除客户", 
                  command=self.delete_customer).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🔄 刷新", 
                  command=self.refresh_customer_list).pack(side=tk.LEFT, padx=5)
    
    def create_license_tab(self, notebook):
        """创建卡密管理标签页"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="🎫 卡密管理")
        
        # 卡密列表
        list_frame = ttk.LabelFrame(frame, text="卡密列表", padding=10)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 创建表格
        columns = ("ID", "客户", "卡密", "类型", "状态", "过期时间")
        self.license_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)
        
        for col in columns:
            self.license_tree.heading(col, text=col)
            if col == "卡密":
                self.license_tree.column(col, width=200)
            else:
                self.license_tree.column(col, width=100)
        
        license_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, 
                                         command=self.license_tree.yview)
        self.license_tree.configure(yscrollcommand=license_scrollbar.set)
        
        self.license_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        license_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 按钮
        button_frame = ttk.Frame(frame)
        button_frame.pack(fill=tk.X, padx=20, pady=10)
        
        ttk.Button(button_frame, text="📋 复制卡密", 
                  command=self.copy_license).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="📄 查看详情", 
                  command=self.view_license_details).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="❌ 禁用卡密",
                  command=self.disable_license).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="✅ 启用卡密",
                  command=self.enable_license).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="📊 导出列表",
                  command=self.export_licenses).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🔄 刷新", 
                  command=self.refresh_license_list).pack(side=tk.LEFT, padx=5)
    
    def create_tools_tab(self, notebook):
        """创建工具标签页"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="🛠️ 工具")
        
        main_frame = ttk.Frame(frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 卡密验证工具
        verify_frame = ttk.LabelFrame(main_frame, text="卡密验证", padding=10)
        verify_frame.pack(fill=tk.X, pady=10)
        
        ttk.Label(verify_frame, text="卡密:").pack(anchor=tk.W)
        self.verify_license_var = tk.StringVar()
        verify_entry = ttk.Entry(verify_frame, textvariable=self.verify_license_var, width=50)
        verify_entry.pack(fill=tk.X, pady=5)
        
        ttk.Button(verify_frame, text="🔍 验证卡密", 
                  command=self.verify_license).pack(anchor=tk.W)
        
        # 硬件信息工具
        hardware_frame = ttk.LabelFrame(main_frame, text="硬件信息", padding=10)
        hardware_frame.pack(fill=tk.X, pady=10)
        
        ttk.Button(hardware_frame, text="📱 获取硬件信息", 
                  command=self.show_hardware_info).pack(anchor=tk.W)
        
        # 数据库工具
        db_frame = ttk.LabelFrame(main_frame, text="数据库管理", padding=10)
        db_frame.pack(fill=tk.X, pady=10)
        
        ttk.Button(db_frame, text="📤 导出数据库", 
                  command=self.export_database).pack(side=tk.LEFT, padx=5)
        ttk.Button(db_frame, text="📥 导入数据库", 
                  command=self.import_database).pack(side=tk.LEFT, padx=5)
        ttk.Button(db_frame, text="🧹 清理过期卡密", 
                  command=self.cleanup_expired).pack(side=tk.LEFT, padx=5)
        
        # 日志显示
        log_frame = ttk.LabelFrame(main_frame, text="操作日志", padding=10)
        log_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        self.log_text = tk.Text(log_frame, height=10, wrap=tk.WORD)
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, 
                                     command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def log_message(self, message):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        print(log_entry.strip())

    def on_license_type_change(self, event=None):
        """授权类型变化处理"""
        license_type = self.license_type_var.get()
        if license_type in LICENSE_TYPES:
            type_info = LICENSE_TYPES[license_type]

            # 更新有效期
            self.duration_var.set(str(type_info["duration_days"]))

            # 更新功能权限
            for feature_key, var in self.feature_vars.items():
                var.set(feature_key in type_info["features"])

    def generate_user_id(self):
        """生成用户ID"""
        import random
        import string

        # 生成格式: USER_YYYYMMDD_XXXX
        date_str = datetime.now().strftime("%Y%m%d")
        random_str = ''.join(random.choices(string.ascii_uppercase + string.digits, k=4))
        user_id = f"USER_{date_str}_{random_str}"

        self.user_id_var.set(user_id)

    def get_current_hardware(self):
        """获取当前设备硬件指纹"""
        try:
            fingerprint = HardwareInfo.get_hardware_fingerprint()
            self.hardware_fp_var.set(fingerprint)
            self.log_message(f"获取硬件指纹: {fingerprint}")
        except Exception as e:
            messagebox.showerror("错误", f"获取硬件指纹失败: {e}")

    def generate_license(self):
        """生成单个卡密"""
        try:
            # 验证输入
            if not self.customer_var.get():
                messagebox.showerror("错误", "请选择客户")
                return

            if not self.user_id_var.get():
                messagebox.showerror("错误", "请输入用户ID")
                return

            # 获取选中的功能
            selected_features = [key for key, var in self.feature_vars.items() if var.get()]
            if not selected_features:
                messagebox.showerror("错误", "请至少选择一个功能")
                return

            # 获取硬件指纹
            hardware_fp = None
            if self.hardware_binding_var.get():
                hardware_fp = self.hardware_fp_var.get().strip()
                if not hardware_fp:
                    if messagebox.askyesno("确认", "未设置硬件指纹，是否继续生成？"):
                        hardware_fp = None
                    else:
                        return

            # 生成卡密
            license_key = LicenseGenerator.generate_license(
                user_id=self.user_id_var.get(),
                license_type=self.license_type_var.get(),
                duration_days=int(self.duration_var.get()),
                features=selected_features,
                hardware_fingerprint=hardware_fp
            )

            # 保存到数据库
            self.save_license_to_db(license_key, selected_features, hardware_fp)

            # 显示结果
            result = f"✅ 卡密生成成功！\n"
            result += f"卡密: {license_key}\n"
            result += f"用户ID: {self.user_id_var.get()}\n"
            result += f"授权类型: {LICENSE_TYPES[self.license_type_var.get()]['name']}\n"
            result += f"有效期: {self.duration_var.get()}天\n"
            result += f"功能权限: {', '.join(selected_features)}\n"
            if hardware_fp:
                result += f"硬件绑定: {hardware_fp}\n"
            result += f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            result += "-" * 50 + "\n"

            self.result_text.insert(tk.END, result)
            self.result_text.see(tk.END)

            self.log_message(f"生成卡密: {license_key} (用户: {self.user_id_var.get()})")

            # 刷新卡密列表
            self.refresh_license_list()

        except Exception as e:
            messagebox.showerror("错误", f"生成卡密失败: {e}")
            self.log_message(f"生成卡密失败: {e}")

    def save_license_to_db(self, license_key, features, hardware_fp):
        """保存卡密到数据库"""
        # 获取客户ID
        customer_name = self.customer_var.get()
        cursor = self.conn.execute("SELECT id FROM customers WHERE name = ?", (customer_name,))
        customer_row = cursor.fetchone()
        customer_id = customer_row[0] if customer_row else None

        # 计算过期时间
        expire_date = datetime.now() + timedelta(days=int(self.duration_var.get()))

        # 插入卡密记录
        self.conn.execute('''
            INSERT INTO licenses (customer_id, license_key, license_type, user_id,
                                features, issue_date, expire_date, hardware_fingerprint, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            customer_id,
            license_key,
            self.license_type_var.get(),
            self.user_id_var.get(),
            json.dumps(features),
            datetime.now().date(),
            expire_date.date(),
            hardware_fp,
            'active'
        ))

        self.conn.commit()

    def batch_generate(self):
        """批量生成卡密"""
        dialog = BatchGenerateDialog(self.root, self)
        dialog.show()

    def show_new_customer_dialog(self):
        """显示新建客户对话框"""
        dialog = CustomerDialog(self.root, self)
        if dialog.show():
            self.refresh_customer_list()

    def refresh_customer_list(self):
        """刷新客户列表"""
        # 清空表格
        for item in self.customer_tree.get_children():
            self.customer_tree.delete(item)

        # 查询客户数据
        cursor = self.conn.execute('''
            SELECT id, name, contact, email, created_at FROM customers ORDER BY created_at DESC
        ''')

        customers = []
        for row in cursor.fetchall():
            self.customer_tree.insert("", tk.END, values=row)
            customers.append(row[1])  # 客户名称

        # 更新下拉框
        if hasattr(self, 'customer_combo'):
            self.customer_combo['values'] = customers

    def refresh_license_list(self):
        """刷新卡密列表"""
        if not hasattr(self, 'license_tree'):
            return

        # 清空表格
        for item in self.license_tree.get_children():
            self.license_tree.delete(item)

        # 查询卡密数据
        cursor = self.conn.execute('''
            SELECT l.id, c.name, l.license_key, l.license_type, l.status, l.expire_date
            FROM licenses l
            LEFT JOIN customers c ON l.customer_id = c.id
            ORDER BY l.created_at DESC
        ''')

        for row in cursor.fetchall():
            # 格式化过期时间
            expire_date = datetime.strptime(row[5], '%Y-%m-%d').date()
            formatted_expire = expire_date.strftime('%Y-%m-%d')

            # 检查状态
            status = row[4]
            if status == 'disabled':
                status_display = '🚫 已禁用'
            elif expire_date < datetime.now().date() and status == 'active':
                status = 'expired'
                status_display = '⏰ 已过期'
            elif status == 'active':
                status_display = '✅ 正常'
            else:
                status_display = f'❓ {status}'

            display_row = (row[0], row[1] or "未知", row[2], row[3], status_display, formatted_expire)
            item_id = self.license_tree.insert("", tk.END, values=display_row)

            # 为不同状态设置不同的标签颜色
            if status == 'disabled':
                self.license_tree.set(item_id, "状态", "🚫 已禁用")
            elif status == 'expired':
                self.license_tree.set(item_id, "状态", "⏰ 已过期")

    def edit_customer(self):
        """编辑客户"""
        selection = self.customer_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择要编辑的客户")
            return

        item = self.customer_tree.item(selection[0])
        customer_id = item['values'][0]

        dialog = CustomerDialog(self.root, self, customer_id)
        if dialog.show():
            self.refresh_customer_list()

    def delete_customer(self):
        """删除客户"""
        selection = self.customer_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择要删除的客户")
            return

        item = self.customer_tree.item(selection[0])
        customer_id = item['values'][0]
        customer_name = item['values'][1]

        if messagebox.askyesno("确认删除", f"确定要删除客户 '{customer_name}' 吗？\n这将同时删除该客户的所有卡密记录。"):
            try:
                # 删除客户的卡密
                self.conn.execute("DELETE FROM licenses WHERE customer_id = ?", (customer_id,))
                # 删除客户
                self.conn.execute("DELETE FROM customers WHERE id = ?", (customer_id,))
                self.conn.commit()

                self.refresh_customer_list()
                self.refresh_license_list()
                self.log_message(f"删除客户: {customer_name}")

            except Exception as e:
                messagebox.showerror("错误", f"删除客户失败: {e}")

    def copy_license(self):
        """复制选中的卡密"""
        selection = self.license_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择要复制的卡密")
            return

        item = self.license_tree.item(selection[0])
        license_key = item['values'][2]

        self.root.clipboard_clear()
        self.root.clipboard_append(license_key)
        messagebox.showinfo("成功", f"卡密已复制到剪贴板:\n{license_key}")

    def view_license_details(self):
        """查看卡密详情"""
        selection = self.license_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择要查看的卡密")
            return

        item = self.license_tree.item(selection[0])
        license_id = item['values'][0]

        dialog = LicenseDetailsDialog(self.root, self, license_id)
        dialog.show()

    def disable_license(self):
        """禁用卡密"""
        selection = self.license_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择要禁用的卡密")
            return

        item = self.license_tree.item(selection[0])
        license_id = item['values'][0]
        license_key = item['values'][2]
        current_status = item['values'][4]

        # 检查当前状态
        if "已禁用" in current_status:
            messagebox.showinfo("提示", "该卡密已经被禁用")
            return

        # 确认禁用
        confirm_msg = f"确定要禁用以下卡密吗？\n\n"
        confirm_msg += f"卡密: {license_key}\n"
        confirm_msg += f"当前状态: {current_status}\n\n"
        confirm_msg += "禁用后该卡密将无法使用，此操作不可撤销！"

        if messagebox.askyesno("确认禁用", confirm_msg):
            try:
                # 更新数据库状态
                self.conn.execute("UPDATE licenses SET status = 'disabled' WHERE id = ?", (license_id,))
                self.conn.commit()

                # 刷新列表
                self.refresh_license_list()

                # 显示成功消息
                messagebox.showinfo("成功", f"卡密已成功禁用\n{license_key}")
                self.log_message(f"禁用卡密: {license_key}")

            except Exception as e:
                messagebox.showerror("错误", f"禁用卡密失败: {e}")
                self.log_message(f"禁用卡密失败: {e}")

    def enable_license(self):
        """启用卡密"""
        selection = self.license_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择要启用的卡密")
            return

        item = self.license_tree.item(selection[0])
        license_id = item['values'][0]
        license_key = item['values'][2]
        current_status = item['values'][4]

        # 检查当前状态
        if "已禁用" not in current_status:
            messagebox.showinfo("提示", "该卡密未被禁用，无需启用")
            return

        # 确认启用
        confirm_msg = f"确定要启用以下卡密吗？\n\n"
        confirm_msg += f"卡密: {license_key}\n"
        confirm_msg += f"当前状态: {current_status}\n\n"
        confirm_msg += "启用后该卡密将恢复正常使用"

        if messagebox.askyesno("确认启用", confirm_msg):
            try:
                # 更新数据库状态
                self.conn.execute("UPDATE licenses SET status = 'active' WHERE id = ?", (license_id,))
                self.conn.commit()

                # 刷新列表
                self.refresh_license_list()

                # 显示成功消息
                messagebox.showinfo("成功", f"卡密已成功启用\n{license_key}")
                self.log_message(f"启用卡密: {license_key}")

            except Exception as e:
                messagebox.showerror("错误", f"启用卡密失败: {e}")
                self.log_message(f"启用卡密失败: {e}")

    def export_licenses(self):
        """导出卡密列表"""
        try:
            filename = filedialog.asksaveasfilename(
                title="导出卡密列表",
                defaultextension=".csv",
                filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")]
            )

            if filename:
                import csv

                with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # 写入标题
                    writer.writerow(["ID", "客户", "卡密", "类型", "用户ID", "功能", "发放日期", "过期日期", "状态", "硬件指纹"])

                    # 查询数据
                    cursor = self.conn.execute('''
                        SELECT l.id, c.name, l.license_key, l.license_type, l.user_id,
                               l.features, l.issue_date, l.expire_date, l.status, l.hardware_fingerprint
                        FROM licenses l
                        LEFT JOIN customers c ON l.customer_id = c.id
                        ORDER BY l.created_at DESC
                    ''')

                    for row in cursor.fetchall():
                        writer.writerow(row)

                messagebox.showinfo("成功", f"卡密列表已导出到: {filename}")
                self.log_message(f"导出卡密列表: {filename}")

        except Exception as e:
            messagebox.showerror("错误", f"导出失败: {e}")

    def verify_license(self):
        """验证卡密"""
        license_key = self.verify_license_var.get().strip()
        if not license_key:
            messagebox.showerror("错误", "请输入要验证的卡密")
            return

        try:
            # 解码卡密
            license_data = CryptoUtils.decode_license_data(license_key)

            # 显示验证结果
            result = "🔍 卡密验证结果\n"
            result += "=" * 30 + "\n"
            result += f"✅ 卡密格式: 有效\n"
            result += f"📋 版本: {license_data.get('version', 'N/A')}\n"
            result += f"👤 用户ID: {license_data.get('user_id', 'N/A')}\n"
            result += f"🏷️ 授权类型: {license_data.get('license_type', 'N/A')}\n"
            result += f"📅 发放时间: {license_data.get('issue_time', 'N/A')}\n"
            result += f"⏰ 过期时间: {license_data.get('expire_time', 'N/A')}\n"
            result += f"🔧 功能权限: {', '.join(license_data.get('features', []))}\n"
            result += f"💻 硬件指纹: {license_data.get('hardware_fingerprint', '无')}\n"
            result += f"🔐 签名: {license_data.get('signature', 'N/A')}\n"

            # 检查数据库状态
            db_status = self.check_license_status_in_db(license_data.get('user_id'), license_key)

            # 检查是否过期
            try:
                expire_time = datetime.fromisoformat(license_data['expire_time'])
                if db_status == 'disabled':
                    result += f"🚫 状态: 已禁用\n"
                elif datetime.now() > expire_time:
                    result += f"⚠️ 状态: 已过期\n"
                else:
                    remaining_days = (expire_time - datetime.now()).days
                    result += f"✅ 状态: 有效 (剩余{remaining_days}天)\n"
            except:
                if db_status == 'disabled':
                    result += f"🚫 状态: 已禁用\n"
                else:
                    result += f"❓ 状态: 无法确定\n"

            messagebox.showinfo("验证结果", result)
            self.log_message(f"验证卡密: {license_key[:20]}...")

        except Exception as e:
            messagebox.showerror("验证失败", f"卡密验证失败: {e}")

    def check_license_status_in_db(self, user_id, license_key):
        """检查数据库中的卡密状态"""
        try:
            cursor = self.conn.execute(
                "SELECT status FROM licenses WHERE user_id = ? OR license_key = ?",
                (user_id, license_key)
            )
            row = cursor.fetchone()
            if row:
                return row[0]
            else:
                return 'unknown'  # 数据库中找不到
        except Exception as e:
            print(f"检查状态失败: {e}")
            return 'unknown'

    def show_hardware_info(self):
        """显示硬件信息"""
        try:
            hardware_info = HardwareInfo.get_hardware_info()

            info_text = "💻 当前设备硬件信息\n"
            info_text += "=" * 30 + "\n"
            for key, value in hardware_info.items():
                info_text += f"{key}: {value}\n"

            # 创建信息显示窗口
            info_window = tk.Toplevel(self.root)
            info_window.title("硬件信息")
            info_window.geometry("600x400")

            text_widget = tk.Text(info_window, wrap=tk.WORD)
            scrollbar = ttk.Scrollbar(info_window, orient=tk.VERTICAL, command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)

            text_widget.insert(tk.END, info_text)
            text_widget.config(state=tk.DISABLED)

            text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            self.log_message("查看硬件信息")

        except Exception as e:
            messagebox.showerror("错误", f"获取硬件信息失败: {e}")

    def export_database(self):
        """导出数据库"""
        try:
            filename = filedialog.asksaveasfilename(
                title="导出数据库",
                defaultextension=".db",
                filetypes=[("SQLite数据库", "*.db"), ("所有文件", "*.*")]
            )

            if filename:
                import shutil
                shutil.copy2(self.db_path, filename)
                messagebox.showinfo("成功", f"数据库已导出到: {filename}")
                self.log_message(f"导出数据库: {filename}")

        except Exception as e:
            messagebox.showerror("错误", f"导出数据库失败: {e}")

    def import_database(self):
        """导入数据库"""
        if messagebox.askyesno("确认导入", "导入数据库将覆盖当前所有数据，确定继续吗？"):
            try:
                filename = filedialog.askopenfilename(
                    title="选择数据库文件",
                    filetypes=[("SQLite数据库", "*.db"), ("所有文件", "*.*")]
                )

                if filename:
                    import shutil
                    self.conn.close()
                    shutil.copy2(filename, self.db_path)
                    self.conn = sqlite3.connect(self.db_path)

                    self.refresh_customer_list()
                    self.refresh_license_list()

                    messagebox.showinfo("成功", "数据库导入成功")
                    self.log_message(f"导入数据库: {filename}")

            except Exception as e:
                messagebox.showerror("错误", f"导入数据库失败: {e}")

    def cleanup_expired(self):
        """清理过期卡密"""
        if messagebox.askyesno("确认清理", "确定要清理所有过期的卡密吗？"):
            try:
                cursor = self.conn.execute('''
                    UPDATE licenses SET status = 'expired'
                    WHERE expire_date < date('now') AND status = 'active'
                ''')

                affected_rows = cursor.rowcount
                self.conn.commit()

                self.refresh_license_list()
                messagebox.showinfo("清理完成", f"已清理 {affected_rows} 个过期卡密")
                self.log_message(f"清理过期卡密: {affected_rows}个")

            except Exception as e:
                messagebox.showerror("错误", f"清理失败: {e}")

    def run(self):
        """运行程序"""
        self.root.mainloop()

    def __del__(self):
        """析构函数"""
        if hasattr(self, 'conn'):
            self.conn.close()


# 导入对话框类
from dialogs import CustomerDialog, BatchGenerateDialog, LicenseDetailsDialog


def main():
    """主函数"""
    try:
        app = LicenseGeneratorGUI()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")


if __name__ == "__main__":
    main()
