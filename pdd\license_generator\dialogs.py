"""
卡密生成器对话框类
"""
import tkinter as tk
from tkinter import ttk, messagebox
import json
from datetime import datetime, timedelta


class CustomerDialog:
    """客户信息对话框"""
    
    def __init__(self, parent, main_app, customer_id=None):
        self.parent = parent
        self.main_app = main_app
        self.customer_id = customer_id
        self.result = False
        
    def show(self):
        """显示对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("客户信息" if not self.customer_id else "编辑客户")
        self.dialog.geometry("400x300")
        self.dialog.resizable(False, False)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.center_window()
        
        # 创建界面
        self.create_widgets()
        
        # 如果是编辑模式，加载数据
        if self.customer_id:
            self.load_customer_data()
        
        # 等待对话框关闭
        self.dialog.wait_window()
        
        return self.result
    
    def center_window(self):
        """窗口居中"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (300 // 2)
        self.dialog.geometry(f"400x300+{x}+{y}")
    
    def create_widgets(self):
        """创建界面组件"""
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 客户姓名
        ttk.Label(main_frame, text="客户姓名 *:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.name_var = tk.StringVar()
        name_entry = ttk.Entry(main_frame, textvariable=self.name_var, width=30)
        name_entry.grid(row=0, column=1, pady=5, sticky=tk.W)
        
        # 联系方式
        ttk.Label(main_frame, text="联系方式:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.contact_var = tk.StringVar()
        contact_entry = ttk.Entry(main_frame, textvariable=self.contact_var, width=30)
        contact_entry.grid(row=1, column=1, pady=5, sticky=tk.W)
        
        # 邮箱
        ttk.Label(main_frame, text="邮箱:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.email_var = tk.StringVar()
        email_entry = ttk.Entry(main_frame, textvariable=self.email_var, width=30)
        email_entry.grid(row=2, column=1, pady=5, sticky=tk.W)
        
        # 备注
        ttk.Label(main_frame, text="备注:").grid(row=3, column=0, sticky=tk.NW, pady=5)
        self.notes_text = tk.Text(main_frame, width=30, height=5)
        self.notes_text.grid(row=3, column=1, pady=5, sticky=tk.W)
        
        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=2, pady=20)
        
        ttk.Button(button_frame, text="保存", command=self.save_customer).pack(side=tk.LEFT, padx=10)
        ttk.Button(button_frame, text="取消", command=self.cancel).pack(side=tk.LEFT, padx=10)
        
        # 设置焦点
        name_entry.focus()
    
    def load_customer_data(self):
        """加载客户数据"""
        cursor = self.main_app.conn.execute(
            "SELECT name, contact, email, notes FROM customers WHERE id = ?",
            (self.customer_id,)
        )
        row = cursor.fetchone()
        if row:
            self.name_var.set(row[0] or "")
            self.contact_var.set(row[1] or "")
            self.email_var.set(row[2] or "")
            self.notes_text.insert(tk.END, row[3] or "")
    
    def save_customer(self):
        """保存客户信息"""
        name = self.name_var.get().strip()
        if not name:
            messagebox.showerror("错误", "请输入客户姓名")
            return
        
        contact = self.contact_var.get().strip()
        email = self.email_var.get().strip()
        notes = self.notes_text.get(1.0, tk.END).strip()
        
        try:
            if self.customer_id:
                # 更新客户
                self.main_app.conn.execute('''
                    UPDATE customers SET name = ?, contact = ?, email = ?, notes = ?
                    WHERE id = ?
                ''', (name, contact, email, notes, self.customer_id))
                self.main_app.log_message(f"更新客户: {name}")
            else:
                # 新建客户
                self.main_app.conn.execute('''
                    INSERT INTO customers (name, contact, email, notes)
                    VALUES (?, ?, ?, ?)
                ''', (name, contact, email, notes))
                self.main_app.log_message(f"新建客户: {name}")
            
            self.main_app.conn.commit()
            self.result = True
            self.dialog.destroy()
            
        except Exception as e:
            messagebox.showerror("错误", f"保存客户信息失败: {e}")
    
    def cancel(self):
        """取消"""
        self.result = False
        self.dialog.destroy()


class BatchGenerateDialog:
    """批量生成对话框"""
    
    def __init__(self, parent, main_app):
        self.parent = parent
        self.main_app = main_app
        
    def show(self):
        """显示对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("批量生成卡密")
        self.dialog.geometry("500x400")
        self.dialog.resizable(False, False)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.center_window()
        
        # 创建界面
        self.create_widgets()
        
        # 等待对话框关闭
        self.dialog.wait_window()
    
    def center_window(self):
        """窗口居中"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (400 // 2)
        self.dialog.geometry(f"500x400+{x}+{y}")
    
    def create_widgets(self):
        """创建界面组件"""
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 生成数量
        ttk.Label(main_frame, text="生成数量:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.count_var = tk.StringVar(value="10")
        count_entry = ttk.Entry(main_frame, textvariable=self.count_var, width=10)
        count_entry.grid(row=0, column=1, pady=5, sticky=tk.W)
        
        # 用户ID前缀
        ttk.Label(main_frame, text="用户ID前缀:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.prefix_var = tk.StringVar(value="BATCH")
        prefix_entry = ttk.Entry(main_frame, textvariable=self.prefix_var, width=20)
        prefix_entry.grid(row=1, column=1, pady=5, sticky=tk.W)
        
        # 授权类型
        ttk.Label(main_frame, text="授权类型:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.license_type_var = tk.StringVar(value="personal")
        from license_system.config import LICENSE_TYPES
        license_type_combo = ttk.Combobox(main_frame, textvariable=self.license_type_var,
                                         values=list(LICENSE_TYPES.keys()), state="readonly")
        license_type_combo.grid(row=2, column=1, pady=5, sticky=tk.W)
        
        # 有效期
        ttk.Label(main_frame, text="有效期(天):").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.duration_var = tk.StringVar(value="365")
        duration_entry = ttk.Entry(main_frame, textvariable=self.duration_var, width=10)
        duration_entry.grid(row=3, column=1, pady=5, sticky=tk.W)
        
        # 硬件绑定
        self.hardware_binding_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(main_frame, text="启用硬件绑定", 
                       variable=self.hardware_binding_var).grid(row=4, column=0, columnspan=2, sticky=tk.W, pady=5)
        
        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=5, column=0, columnspan=2, pady=20)
        
        ttk.Button(button_frame, text="开始生成", command=self.start_generate).pack(side=tk.LEFT, padx=10)
        ttk.Button(button_frame, text="取消", command=self.cancel).pack(side=tk.LEFT, padx=10)
        
        # 进度显示
        progress_frame = ttk.LabelFrame(main_frame, text="生成进度", padding=10)
        progress_frame.grid(row=6, column=0, columnspan=2, sticky=tk.EW, pady=10)
        
        self.progress_var = tk.StringVar(value="准备就绪")
        ttk.Label(progress_frame, textvariable=self.progress_var).pack()
        
        self.progress_bar = ttk.Progressbar(progress_frame, mode='determinate')
        self.progress_bar.pack(fill=tk.X, pady=5)
    
    def start_generate(self):
        """开始批量生成"""
        try:
            count = int(self.count_var.get())
            if count <= 0 or count > 1000:
                messagebox.showerror("错误", "生成数量必须在1-1000之间")
                return
            
            prefix = self.prefix_var.get().strip()
            if not prefix:
                messagebox.showerror("错误", "请输入用户ID前缀")
                return
            
            # 获取授权类型的功能
            license_type = self.license_type_var.get()
            from license_system.config import LICENSE_TYPES
            features = LICENSE_TYPES[license_type]["features"]
            
            # 开始生成
            self.progress_bar['maximum'] = count
            generated_licenses = []
            
            for i in range(count):
                # 更新进度
                self.progress_var.set(f"正在生成第 {i+1}/{count} 个卡密...")
                self.progress_bar['value'] = i + 1
                self.dialog.update()
                
                # 生成用户ID
                user_id = f"{prefix}_{datetime.now().strftime('%Y%m%d')}_{i+1:04d}"
                
                # 生成卡密
                from license_system.crypto_utils import LicenseGenerator
                license_key = LicenseGenerator.generate_license(
                    user_id=user_id,
                    license_type=license_type,
                    duration_days=int(self.duration_var.get()),
                    features=features,
                    hardware_fingerprint=None if not self.hardware_binding_var.get() else ""
                )
                
                generated_licenses.append({
                    'user_id': user_id,
                    'license_key': license_key,
                    'license_type': license_type,
                    'features': features
                })
                
                # 保存到数据库
                expire_date = datetime.now() + timedelta(days=int(self.duration_var.get()))
                
                self.main_app.conn.execute('''
                    INSERT INTO licenses (customer_id, license_key, license_type, user_id, 
                                        features, issue_date, expire_date, hardware_fingerprint, status)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    None,  # 批量生成不关联客户
                    license_key,
                    license_type,
                    user_id,
                    json.dumps(features),
                    datetime.now().date(),
                    expire_date.date(),
                    None,
                    'active'
                ))
            
            self.main_app.conn.commit()
            
            # 生成完成
            self.progress_var.set(f"生成完成！共生成 {count} 个卡密")
            
            # 显示结果
            self.show_batch_result(generated_licenses)
            
            # 刷新主界面
            self.main_app.refresh_license_list()
            self.main_app.log_message(f"批量生成卡密: {count}个")
            
        except ValueError:
            messagebox.showerror("错误", "请输入有效的数字")
        except Exception as e:
            messagebox.showerror("错误", f"批量生成失败: {e}")
    
    def show_batch_result(self, licenses):
        """显示批量生成结果"""
        result_window = tk.Toplevel(self.dialog)
        result_window.title("批量生成结果")
        result_window.geometry("800x600")
        
        # 创建文本框显示结果
        text_frame = ttk.Frame(result_window)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        text_widget = tk.Text(text_frame, wrap=tk.NONE)
        v_scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
        h_scrollbar = ttk.Scrollbar(text_frame, orient=tk.HORIZONTAL, command=text_widget.xview)
        text_widget.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # 添加结果内容
        content = "批量生成卡密结果\n"
        content += "=" * 80 + "\n"
        content += f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        content += f"授权类型: {self.license_type_var.get()}\n"
        content += f"有效期: {self.duration_var.get()}天\n"
        content += f"总数量: {len(licenses)}\n"
        content += "-" * 80 + "\n\n"
        
        for i, license_info in enumerate(licenses, 1):
            content += f"{i:3d}. 用户ID: {license_info['user_id']}\n"
            content += f"     卡密: {license_info['license_key']}\n\n"
        
        text_widget.insert(tk.END, content)
        text_widget.config(state=tk.DISABLED)
        
        # 布局
        text_widget.grid(row=0, column=0, sticky=tk.NSEW)
        v_scrollbar.grid(row=0, column=1, sticky=tk.NS)
        h_scrollbar.grid(row=1, column=0, sticky=tk.EW)
        
        text_frame.grid_rowconfigure(0, weight=1)
        text_frame.grid_columnconfigure(0, weight=1)
        
        # 保存按钮
        button_frame = ttk.Frame(result_window)
        button_frame.pack(fill=tk.X, padx=10, pady=5)
        
        def save_to_file():
            from tkinter import filedialog
            filename = filedialog.asksaveasfilename(
                title="保存卡密列表",
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
            )
            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(content)
                messagebox.showinfo("成功", f"卡密列表已保存到: {filename}")
        
        ttk.Button(button_frame, text="💾 保存到文件", command=save_to_file).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="❌ 关闭", command=result_window.destroy).pack(side=tk.RIGHT, padx=5)
    
    def cancel(self):
        """取消"""
        self.dialog.destroy()


class LicenseDetailsDialog:
    """卡密详情对话框"""
    
    def __init__(self, parent, main_app, license_id):
        self.parent = parent
        self.main_app = main_app
        self.license_id = license_id
        
    def show(self):
        """显示对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("卡密详情")
        self.dialog.geometry("600x500")
        self.dialog.resizable(True, True)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.center_window()
        
        # 创建界面
        self.create_widgets()
        
        # 加载数据
        self.load_license_data()
        
        # 等待对话框关闭
        self.dialog.wait_window()
    
    def center_window(self):
        """窗口居中"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (500 // 2)
        self.dialog.geometry(f"600x500+{x}+{y}")
    
    def create_widgets(self):
        """创建界面组件"""
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建文本框显示详情
        self.text_widget = tk.Text(main_frame, wrap=tk.WORD, state=tk.DISABLED)
        scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=self.text_widget.yview)
        self.text_widget.configure(yscrollcommand=scrollbar.set)
        
        self.text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 按钮
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(fill=tk.X, padx=20, pady=10)
        
        ttk.Button(button_frame, text="📋 复制卡密", command=self.copy_license).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="❌ 关闭", command=self.dialog.destroy).pack(side=tk.RIGHT, padx=5)
    
    def load_license_data(self):
        """加载卡密数据"""
        cursor = self.main_app.conn.execute('''
            SELECT l.*, c.name as customer_name
            FROM licenses l
            LEFT JOIN customers c ON l.customer_id = c.id
            WHERE l.id = ?
        ''', (self.license_id,))
        
        row = cursor.fetchone()
        if not row:
            messagebox.showerror("错误", "未找到卡密信息")
            self.dialog.destroy()
            return
        
        # 解析数据
        license_data = {
            'id': row[0],
            'customer_id': row[1],
            'license_key': row[2],
            'license_type': row[3],
            'user_id': row[4],
            'features': json.loads(row[5]) if row[5] else [],
            'issue_date': row[6],
            'expire_date': row[7],
            'hardware_fingerprint': row[8],
            'status': row[9],
            'notes': row[10],
            'created_at': row[11],
            'customer_name': row[12]
        }
        
        # 显示详情
        self.text_widget.config(state=tk.NORMAL)
        self.text_widget.delete(1.0, tk.END)
        
        content = "🎫 卡密详细信息\n"
        content += "=" * 50 + "\n\n"
        
        content += f"📋 基本信息:\n"
        content += f"  卡密ID: {license_data['id']}\n"
        content += f"  卡密: {license_data['license_key']}\n"
        content += f"  用户ID: {license_data['user_id']}\n"
        content += f"  客户: {license_data['customer_name'] or '未关联'}\n\n"
        
        content += f"🏷️ 授权信息:\n"
        from license_system.config import LICENSE_TYPES
        type_name = LICENSE_TYPES.get(license_data['license_type'], {}).get('name', '未知')
        content += f"  授权类型: {type_name} ({license_data['license_type']})\n"
        content += f"  状态: {license_data['status']}\n"
        content += f"  发放日期: {license_data['issue_date']}\n"
        content += f"  过期日期: {license_data['expire_date']}\n"
        
        # 计算剩余天数
        try:
            expire_date = datetime.strptime(license_data['expire_date'], '%Y-%m-%d').date()
            remaining_days = (expire_date - datetime.now().date()).days
            if remaining_days > 0:
                content += f"  剩余天数: {remaining_days}天\n"
            else:
                content += f"  已过期: {abs(remaining_days)}天\n"
        except:
            content += f"  剩余天数: 无法计算\n"
        
        content += f"\n🔧 功能权限:\n"
        for feature in license_data['features']:
            from license_system.config import FEATURES
            feature_name = FEATURES.get(feature, feature)
            content += f"  ✅ {feature_name} ({feature})\n"
        
        content += f"\n💻 硬件绑定:\n"
        if license_data['hardware_fingerprint']:
            content += f"  硬件指纹: {license_data['hardware_fingerprint']}\n"
        else:
            content += f"  硬件指纹: 未绑定\n"
        
        if license_data['notes']:
            content += f"\n📝 备注:\n"
            content += f"  {license_data['notes']}\n"
        
        content += f"\n⏰ 创建时间: {license_data['created_at']}\n"
        
        self.text_widget.insert(tk.END, content)
        self.text_widget.config(state=tk.DISABLED)
        
        # 保存卡密用于复制
        self.license_key = license_data['license_key']
    
    def copy_license(self):
        """复制卡密"""
        self.dialog.clipboard_clear()
        self.dialog.clipboard_append(self.license_key)
        messagebox.showinfo("成功", f"卡密已复制到剪贴板:\n{self.license_key}")
