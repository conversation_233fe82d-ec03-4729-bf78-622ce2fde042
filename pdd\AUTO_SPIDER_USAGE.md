# 🚀 Auto Spider 使用指南

增强版的自动环境配置和打包脚本，支持在打包前选择不同的版本配置。

## ✨ 新增功能

### 🔧 版本配置选择
- **Release版本**：正式发布版，启用完整的卡密验证
- **Dev版本**：开发调试版，放宽验证限制，增加调试功能

### 📋 配置差异对比

| 配置项 | Release版本 | Dev版本 |
|--------|-------------|---------|
| 版本号 | 1.0.0 | 1.0.0-dev |
| 调试模式 | ❌ 关闭 | ✅ 开启 |
| 试用模式 | ❌ 关闭 | ✅ 开启 |
| 时间检查 | ✅ 严格 | ❌ 跳过 |
| 硬件绑定 | ✅ 启用 | ❌ 禁用 |
| 检查间隔 | 3600秒 | 60秒 |
| 试用期限 | 7天 | 30天 |
| 日志级别 | INFO | DEBUG |
| 控制台日志 | ❌ 关闭 | ✅ 开启 |
| 调试工具 | ❌ 隐藏 | ✅ 显示 |

## 🚀 使用方法

### 1. 交互式使用（推荐）
```bash
python auto_spider.py
```
程序会引导您：
1. 选择版本配置（release/dev）
2. 确认配置详情
3. 自动应用配置并开始构建

### 2. 命令行参数
```bash
# 指定构建类型
python auto_spider.py --type release
python auto_spider.py --type dev
python auto_spider.py --type both

# 指定版本配置
python auto_spider.py --version-config release
python auto_spider.py --version-config dev

# 跳过某些步骤
python auto_spider.py --skip-deps      # 跳过依赖安装
python auto_spider.py --skip-chrome    # 跳过Chrome设置
python auto_spider.py --skip-version   # 跳过版本配置

# 组合使用
python auto_spider.py --type release --version-config release --skip-chrome
```

### 3. 完整示例
```bash
# 构建发布版本
python auto_spider.py --type release --version-config release

# 构建开发版本
python auto_spider.py --type dev --version-config dev

# 同时构建两个版本
python auto_spider.py --type both --version-config release
```

## 📁 生成的文件

### 配置文件
- `license_system/config.py` - 卡密系统配置
- `app_config.py` - 应用程序配置
- `VERSION` - 版本标识文件
- `build_config_*.json` - 构建配置文件

### 可执行文件
- **Release版本**：`dist/拼多多采集工具_1.0.0.exe`
- **Dev版本**：`dist/拼多多采集工具_1.0.0-dev_dev.exe`

## 🔧 版本配置详情

### Release版本特点
- ✅ 完整的卡密验证机制
- ✅ 硬件绑定防盗用
- ✅ 严格的时间检查
- ✅ 生产环境优化
- ❌ 无调试信息输出

### Dev版本特点
- ✅ 放宽的验证机制
- ✅ 详细的调试信息
- ✅ 开发者工具
- ✅ 扩展的试用期
- ❌ 无硬件绑定

## 🛠️ 自定义配置

您可以修改 `auto_spider.py` 中的 `VERSION_CONFIGS` 字典来自定义版本配置：

```python
VERSION_CONFIGS = {
    "custom": {
        "version": "1.0.0-custom",
        "debug": True,
        "trial_mode": False,
        # ... 其他配置
    }
}
```

## 📋 构建流程

1. **环境检查**
   - Python版本检查
   - pip可用性检查

2. **版本配置**
   - 交互式选择或命令行指定
   - 应用配置到项目文件

3. **依赖安装**
   - 基础包安装
   - 根据构建类型安装额外依赖

4. **Chrome设置**
   - 检查便携版Chrome
   - 提供设置指导

5. **卡密系统检查**
   - 验证必要文件存在
   - 根据构建类型决定是否包含

6. **构建执行**
   - 生成PyInstaller配置
   - 执行打包命令
   - 输出结果信息

## ⚠️ 注意事项

1. **Release构建**需要完整的 `license_system` 目录
2. **版本配置**会覆盖现有的配置文件
3. **构建前**建议备份重要配置文件
4. **Chrome设置**是可选的，可以使用系统Chrome

## 🔍 故障排除

### 常见问题

#### 1. 卡密系统文件缺失
```
❌ Missing license system files: config.py, crypto_utils.py
```
**解决方案**：确保 `license_system` 目录完整

#### 2. 依赖安装失败
```
❌ Failed to install cryptography
```
**解决方案**：手动安装或使用 `--skip-deps` 跳过

#### 3. 构建失败
```
❌ Build failed! (release)
```
**解决方案**：检查错误输出，确保所有依赖已安装

### 调试技巧

1. 使用 `--skip-deps` 跳过依赖安装
2. 使用 `--skip-chrome` 跳过Chrome设置
3. 查看生成的 `build_config_*.json` 文件
4. 检查 `VERSION` 文件确认配置应用

## 🎯 最佳实践

1. **开发阶段**：使用dev配置进行快速测试
2. **发布前**：使用release配置进行最终构建
3. **版本管理**：通过VERSION文件跟踪构建信息
4. **配置备份**：重要配置文件建议版本控制

---

**版本**: v2.0.0  
**更新时间**: 2024-07-31  
**新增功能**: 版本配置选择、增强的构建流程
