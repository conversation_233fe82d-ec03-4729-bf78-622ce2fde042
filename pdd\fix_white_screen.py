"""
修复无头模式切换后的白屏问题
"""
import time

class BrowserDisplayFixer:
    """浏览器显示修复器"""
    
    def __init__(self, spider):
        self.spider = spider
        self.last_known_url = None
        self.page_history = []
    
    def record_page_visit(self, url):
        """记录页面访问"""
        self.last_known_url = url
        if url not in self.page_history:
            self.page_history.append(url)
        
        # 只保留最近10个页面
        if len(self.page_history) > 10:
            self.page_history.pop(0)
    
    def detect_white_screen(self, tab):
        """检测白屏"""
        try:
            current_url = tab.url
            page_title = tab.title
            
            # 检测白屏的条件
            white_screen_indicators = [
                not current_url,
                current_url == "about:blank",
                "data:" in current_url,
                not page_title,
                page_title.strip() == "",
                page_title == "about:blank"
            ]
            
            return any(white_screen_indicators)
            
        except Exception:
            return True  # 如果无法获取信息，假设是白屏
    
    def fix_white_screen(self, tab, target_url=None):
        """修复白屏"""
        try:
            self.spider.log_callback("检测到白屏，正在修复...", "INFO")
            
            # 确定要访问的URL
            if target_url:
                url_to_visit = target_url
            elif self.last_known_url:
                url_to_visit = self.last_known_url
            elif self.page_history:
                url_to_visit = self.page_history[-1]  # 最近访问的页面
            else:
                url_to_visit = "https://mobile.pinduoduo.com"  # 默认首页
            
            self.spider.log_callback(f"正在重新加载页面: {url_to_visit}", "INFO")
            
            # 重新加载页面
            tab.get(url_to_visit)
            time.sleep(3)
            
            # 验证修复结果
            if not self.detect_white_screen(tab):
                self.spider.log_callback("白屏修复成功", "SUCCESS")
                return True
            else:
                # 如果还是白屏，尝试刷新
                self.spider.log_callback("尝试刷新页面...", "INFO")
                tab.refresh()
                time.sleep(2)
                
                if not self.detect_white_screen(tab):
                    self.spider.log_callback("刷新后白屏修复成功", "SUCCESS")
                    return True
                else:
                    self.spider.log_callback("白屏修复失败", "ERROR")
                    return False
                    
        except Exception as e:
            self.spider.log_callback(f"白屏修复过程出错: {str(e)}", "ERROR")
            return False
    
    def safe_show_browser(self, tab):
        """安全显示浏览器（自动修复白屏）"""
        try:
            # 显示浏览器
            tab.set.window.show()
            time.sleep(1)
            
            # 检查是否白屏
            if self.detect_white_screen(tab):
                self.fix_white_screen(tab)
            
            return True
            
        except Exception as e:
            self.spider.log_callback(f"显示浏览器失败: {str(e)}", "ERROR")
            return False

def create_enhanced_spider_methods(spider):
    """为spider添加增强的显示方法"""
    
    # 创建修复器
    fixer = BrowserDisplayFixer(spider)
    
    # 保存原始方法
    original_get = spider.tab1.get if spider.tab1 else None
    
    def enhanced_get(url):
        """增强的页面访问方法"""
        if original_get:
            result = original_get(url)
            fixer.record_page_visit(url)
            return result
    
    def enhanced_toggle_visibility():
        """增强的显示切换方法"""
        try:
            if not spider.browser or not spider.tab1:
                spider.log_callback("浏览器未初始化", "WARNING")
                return False
            
            if spider.browser_visible:
                # 隐藏浏览器
                spider.tab1.set.window.hide()
                spider.browser_visible = False
                spider.log_callback("浏览器已隐藏", "INFO")
            else:
                # 安全显示浏览器
                spider.browser_visible = fixer.safe_show_browser(spider.tab1)
                if spider.browser_visible:
                    spider.log_callback("浏览器已显示", "INFO")
            
            return spider.browser_visible
            
        except Exception as e:
            spider.log_callback(f"切换浏览器显示失败: {str(e)}", "ERROR")
            return False
    
    def enhanced_show_for_verify():
        """增强的验证显示方法"""
        try:
            if not spider.browser or not spider.tab1:
                spider.log_callback("浏览器未初始化", "WARNING")
                return False
            
            # 安全显示浏览器
            success = fixer.safe_show_browser(spider.tab1)
            if success:
                spider.browser_visible = True
                spider.log_callback("浏览器已显示，请手动完成验证", "INFO")
                spider.log_callback("提示：完成验证后可以点击'隐藏浏览器'按钮", "INFO")
            
            return success
            
        except Exception as e:
            spider.log_callback(f"显示浏览器失败: {str(e)}", "ERROR")
            return False
    
    # 替换spider的方法
    if spider.tab1:
        spider.tab1.get = enhanced_get
    
    spider.toggle_browser_visibility = enhanced_toggle_visibility
    spider.show_browser_for_manual_verify = enhanced_show_for_verify
    spider._display_fixer = fixer
    
    return fixer

# 使用示例
def apply_white_screen_fix(spider):
    """应用白屏修复到spider实例"""
    return create_enhanced_spider_methods(spider)
