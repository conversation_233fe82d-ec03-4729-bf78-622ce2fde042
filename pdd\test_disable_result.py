import sys
import os
import sqlite3
from datetime import datetime, timedelta

sys.path.insert(0, '.')

def write_result(message):
    with open("disable_test_result.txt", "a", encoding="utf-8") as f:
        f.write(message + "\n")

# 清空文件
with open("disable_test_result.txt", "w", encoding="utf-8") as f:
    f.write("")

try:
    write_result("🧪 测试卡密禁用功能")
    write_result("=" * 50)
    
    # 创建测试数据库
    test_db = "test_license_data.db"
    if os.path.exists(test_db):
        os.remove(test_db)
    
    conn = sqlite3.connect(test_db)
    
    # 创建表结构
    conn.execute('''
        CREATE TABLE IF NOT EXISTS licenses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            customer_id INTEGER,
            license_key TEXT NOT NULL,
            license_type TEXT NOT NULL,
            user_id TEXT NOT NULL,
            features TEXT,
            issue_date DATE,
            expire_date DATE,
            hardware_fingerprint TEXT,
            status TEXT DEFAULT 'active',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 插入测试数据
    test_licenses = [
        ("TEST-AAAA-BBBB-CCCC-DDDD", "personal", "USER001", "active"),
        ("TEST-EEEE-FFFF-GGGG-HHHH", "professional", "USER002", "active"),
        ("TEST-IIII-JJJJ-KKKK-LLLL", "enterprise", "USER003", "disabled"),
    ]
    
    for license_key, license_type, user_id, status in test_licenses:
        conn.execute('''
            INSERT INTO licenses (license_key, license_type, user_id, status, 
                                issue_date, expire_date, features)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            license_key, license_type, user_id, status,
            datetime.now().date(),
            (datetime.now() + timedelta(days=365)).date(),
            '["basic"]'
        ))
    
    conn.commit()
    write_result("✅ 测试数据创建成功")
    
    # 查询初始状态
    write_result("\n📋 初始卡密状态:")
    cursor = conn.execute("SELECT id, license_key, status FROM licenses ORDER BY id")
    for row in cursor.fetchall():
        write_result(f"  ID: {row[0]}, 卡密: {row[1]}, 状态: {row[2]}")
    
    # 测试禁用功能
    write_result("\n🚫 测试禁用功能...")
    license_id = 1  # 禁用第一个卡密
    
    # 执行禁用操作
    conn.execute("UPDATE licenses SET status = 'disabled' WHERE id = ?", (license_id,))
    conn.commit()
    write_result(f"✅ 卡密 ID {license_id} 已禁用")
    
    # 查询禁用后状态
    write_result("\n📋 禁用后卡密状态:")
    cursor = conn.execute("SELECT id, license_key, status FROM licenses ORDER BY id")
    for row in cursor.fetchall():
        status_display = "🚫 已禁用" if row[2] == 'disabled' else "✅ 正常"
        write_result(f"  ID: {row[0]}, 卡密: {row[1]}, 状态: {status_display}")
    
    # 测试启用功能
    write_result("\n✅ 测试启用功能...")
    license_id = 3  # 启用第三个卡密（原本是禁用的）
    
    # 执行启用操作
    conn.execute("UPDATE licenses SET status = 'active' WHERE id = ?", (license_id,))
    conn.commit()
    write_result(f"✅ 卡密 ID {license_id} 已启用")
    
    # 查询启用后状态
    write_result("\n📋 启用后卡密状态:")
    cursor = conn.execute("SELECT id, license_key, status FROM licenses ORDER BY id")
    for row in cursor.fetchall():
        status_display = "🚫 已禁用" if row[2] == 'disabled' else "✅ 正常"
        write_result(f"  ID: {row[0]}, 卡密: {row[1]}, 状态: {status_display}")
    
    # 统计状态
    write_result("\n📊 状态统计:")
    cursor = conn.execute("SELECT status, COUNT(*) FROM licenses GROUP BY status")
    for row in cursor.fetchall():
        status_name = "已禁用" if row[0] == 'disabled' else "正常"
        write_result(f"  {status_name}: {row[1]} 个")
    
    write_result("\n🎉 禁用功能测试完成！")
    
    conn.close()
    if os.path.exists(test_db):
        os.remove(test_db)
    
except Exception as e:
    write_result(f"❌ 测试失败: {e}")
    import traceback
    write_result(traceback.format_exc())

write_result("测试完成")
