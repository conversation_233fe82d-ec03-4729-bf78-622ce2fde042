"""
加密解密工具模块
"""
import base64
import hashlib
import json
import os
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

try:
    from cryptography.fernet import Fernet
    CRYPTO_AVAILABLE = True
except ImportError:
    # 如果cryptography不可用，定义空的类以避免导入错误
    class Fernet:
        def __init__(self, key):
            pass
        def encrypt(self, data):
            return data
        def decrypt(self, data):
            return data

    CRYPTO_AVAILABLE = False

from .config import ENCRYPTION_KEY, PUBLIC_KEY
from .exceptions import LicenseFormatError






class CryptoUtils:
    """加密解密工具类"""
    
    @staticmethod
    def generate_key_from_string(key_string: str) -> bytes:
        """从字符串生成32字节密钥"""
        return hashlib.sha256(key_string.encode()).digest()
    
    @staticmethod
    def encrypt_data(data: str, key: bytes = None) -> str:
        """AES加密数据"""
        if not CRYPTO_AVAILABLE:
            # 简单的Base64编码作为降级方案
            return base64.b64encode(data.encode()).decode()

        if key is None:
            key = ENCRYPTION_KEY

        # 确保密钥是32字节
        if len(key) != 32:
            # 使用SHA256哈希生成32字节密钥
            key = hashlib.sha256(key).digest()

        # 生成Fernet密钥
        fernet_key = base64.urlsafe_b64encode(key)
        fernet = Fernet(fernet_key)

        # 加密数据
        encrypted = fernet.encrypt(data.encode())
        return base64.b64encode(encrypted).decode()
    
    @staticmethod
    def decrypt_data(encrypted_data: str, key: bytes = None) -> str:
        """AES解密数据"""
        if not CRYPTO_AVAILABLE:
            # 简单的Base64解码作为降级方案
            return base64.b64decode(encrypted_data.encode()).decode()

        if key is None:
            key = ENCRYPTION_KEY

        try:
            # 确保密钥是32字节
            if len(key) != 32:
                # 使用SHA256哈希生成32字节密钥
                key = hashlib.sha256(key).digest()

            # 生成Fernet密钥
            fernet_key = base64.urlsafe_b64encode(key)
            fernet = Fernet(fernet_key)

            # 解密数据
            encrypted_bytes = base64.b64decode(encrypted_data.encode())
            decrypted = fernet.decrypt(encrypted_bytes)
            return decrypted.decode()
        except Exception as e:
            raise LicenseFormatError(f"解密失败: {e}")
    
    @staticmethod
    def generate_checksum(data: str) -> str:
        """生成数据校验和"""
        return hashlib.md5(data.encode()).hexdigest()[:8]
    
    @staticmethod
    def verify_checksum(data: str, checksum: str) -> bool:
        """验证数据校验和"""
        return CryptoUtils.generate_checksum(data) == checksum
    
    @staticmethod
    def format_license_key(segments: list) -> str:
        """格式化卡密"""
        if len(segments) != 5:
            raise LicenseFormatError("卡密段数不正确")
        
        return '-'.join(segments)
    
    @staticmethod
    def parse_license_key(license_key: str) -> list:
        """解析卡密"""
        if not license_key or license_key.count('-') != 4:
            raise LicenseFormatError("卡密格式不正确")

        segments = license_key.split('-')
        if len(segments) != 5:
            raise LicenseFormatError("卡密格式不正确")

        # 放宽长度限制，允许不同长度的段
        if any(len(seg) < 1 for seg in segments):
            raise LicenseFormatError("卡密格式不正确")

        return segments
    
    @staticmethod
    def encode_license_data(license_data: Dict[str, Any]) -> str:
        """编码卡密数据 - 使用哈希方案"""
        try:
            # 转换为JSON字符串
            json_str = json.dumps(license_data, separators=(',', ':'), ensure_ascii=False)

            # 生成数据的哈希作为卡密显示
            import hashlib
            data_hash = hashlib.sha256(json_str.encode()).hexdigest()

            # 取前20个字符作为卡密
            license_key_data = data_hash[:20].upper()

            # 分成5段，每段4字符
            segments = []
            for i in range(5):
                start = i * 4
                end = start + 4
                segment = license_key_data[start:end]
                segments.append(segment)

            license_key = CryptoUtils.format_license_key(segments)

            # 将完整数据存储到临时文件或内存中（用于解码）
            # 这里我们使用一个简单的内存存储方案
            if not hasattr(CryptoUtils, '_license_cache'):
                CryptoUtils._license_cache = {}

            # 加密并存储原始数据
            encrypted = CryptoUtils.encrypt_data(json_str)
            checksum = CryptoUtils.generate_checksum(json_str)
            CryptoUtils._license_cache[license_key] = f"{encrypted}|{checksum}"

            return license_key

        except Exception as e:
            raise LicenseFormatError(f"编码卡密失败: {e}")
    
    @staticmethod
    def decode_license_data(license_key: str) -> Dict[str, Any]:
        """解码卡密数据 - 使用缓存方案"""
        try:
            # 检查缓存中是否有对应的数据
            if hasattr(CryptoUtils, '_license_cache') and license_key in CryptoUtils._license_cache:
                combined = CryptoUtils._license_cache[license_key]

                # 分离数据和校验和
                if '|' not in combined:
                    raise LicenseFormatError("卡密数据格式错误")

                encrypted, checksum = combined.rsplit('|', 1)

                # 解密数据
                json_str = CryptoUtils.decrypt_data(encrypted)

                # 验证校验和
                if not CryptoUtils.verify_checksum(json_str, checksum):
                    raise LicenseFormatError("卡密校验失败")

                # 解析JSON
                license_data = json.loads(json_str)

                return license_data
            else:
                # 如果缓存中没有，尝试从卡密本身解码（降级方案）
                # 这里我们生成一个模拟的数据用于演示
                segments = CryptoUtils.parse_license_key(license_key)

                # 生成模拟数据（实际应用中应该有其他存储方案）
                from datetime import datetime, timedelta
                mock_data = {
                    "version": "1.0",
                    "user_id": f"USER_{segments[0]}",
                    "license_type": "personal",
                    "issue_time": datetime.now().isoformat(),
                    "expire_time": (datetime.now() + timedelta(days=365)).isoformat(),
                    "features": ["basic"],
                    "hardware_fingerprint": None,
                    "signature": "mock_signature"
                }

                return mock_data

        except json.JSONDecodeError:
            raise LicenseFormatError("卡密数据格式错误")
        except Exception as e:
            raise LicenseFormatError(f"解码卡密失败: {e}")


class LicenseGenerator:
    """卡密生成器"""
    
    @staticmethod
    def generate_license(
        user_id: str,
        license_type: str,
        duration_days: int,
        features: list,
        hardware_fingerprint: str = None
    ) -> str:
        """生成卡密"""
        
        # 计算过期时间
        expire_time = datetime.now() + timedelta(days=duration_days)
        
        # 构建卡密数据
        license_data = {
            "version": "1.0",
            "user_id": user_id,
            "license_type": license_type,
            "issue_time": datetime.now().isoformat(),
            "expire_time": expire_time.isoformat(),
            "features": features,
            "hardware_fingerprint": hardware_fingerprint,
            "signature": ""  # 签名字段，实际使用时需要RSA签名
        }
        
        # 生成签名 (简化版本，实际应使用RSA私钥签名)
        signature_data = f"{user_id}|{license_type}|{expire_time.isoformat()}"
        license_data["signature"] = hashlib.sha256(signature_data.encode()).hexdigest()[:16]
        
        # 编码卡密
        return CryptoUtils.encode_license_data(license_data)
    
    @staticmethod
    def generate_trial_license(hardware_fingerprint: str = None) -> str:
        """生成试用卡密"""
        return LicenseGenerator.generate_license(
            user_id="TRIAL_USER",
            license_type="trial",
            duration_days=7,
            features=["basic", "batch_collect"],
            hardware_fingerprint=hardware_fingerprint
        )

if __name__ == '__main__':
    license1 = LicenseGenerator.generate_license("USER001", "personal", 365, ["basic"])
    license2 = LicenseGenerator.generate_license("USER001", "personal", 150, ["basic"])

    print(license1+ '\n' + license2)