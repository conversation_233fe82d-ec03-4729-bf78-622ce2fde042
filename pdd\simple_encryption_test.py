#!/usr/bin/env python3
"""
简化的卡密加密测试
"""
import sys
import os
import json
import base64
import hashlib
from datetime import datetime, timedelta

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_encryption_step_by_step():
    """逐步测试加密过程"""
    print("🔐 卡密加密逐步测试")
    print("=" * 50)
    
    # 1. 模拟卡密数据
    license_data = {
        "version": "1.0",
        "user_id": "TEST_USER_001",
        "license_type": "professional",
        "issue_time": datetime.now().isoformat(),
        "expire_time": (datetime.now() + timedelta(days=365)).isoformat(),
        "features": ["basic", "batch_collect", "export_data"],
        "hardware_fingerprint": "test_hardware_fp",
        "signature": "test_signature"
    }
    
    print("📋 步骤1: 原始卡密数据")
    print(json.dumps(license_data, indent=2, ensure_ascii=False))
    
    # 2. JSON序列化
    json_str = json.dumps(license_data, separators=(',', ':'), ensure_ascii=False)
    print(f"\n📄 步骤2: JSON序列化")
    print(f"长度: {len(json_str)}")
    print(f"内容: {json_str[:100]}...")
    
    # 3. 测试加密（模拟）
    print(f"\n🔐 步骤3: 加密过程")
    
    # 检查是否有cryptography库
    try:
        from cryptography.fernet import Fernet
        
        # 使用真实的加密
        ENCRYPTION_KEY = b"PDD_SPIDER_LICENSE_KEY_2024_SECURE"
        fernet_key = base64.urlsafe_b64encode(ENCRYPTION_KEY)
        fernet = Fernet(fernet_key)
        
        encrypted_bytes = fernet.encrypt(json_str.encode())
        encrypted = base64.b64encode(encrypted_bytes).decode()
        
        print("✅ 使用AES-256加密")
        print(f"加密后长度: {len(encrypted)}")
        print(f"加密内容: {encrypted[:50]}...")
        
        # 验证解密
        decrypted_bytes = base64.b64decode(encrypted.encode())
        decrypted_str = fernet.decrypt(decrypted_bytes).decode()
        
        if decrypted_str == json_str:
            print("✅ 解密验证成功")
        else:
            print("❌ 解密验证失败")
            
    except ImportError:
        # 使用Base64降级方案
        encrypted = base64.b64encode(json_str.encode()).decode()
        print("⚠️ 使用Base64降级方案")
        print(f"编码后长度: {len(encrypted)}")
        print(f"编码内容: {encrypted[:50]}...")
        
        # 验证解码
        decrypted_str = base64.b64decode(encrypted.encode()).decode()
        if decrypted_str == json_str:
            print("✅ 解码验证成功")
        else:
            print("❌ 解码验证失败")
    
    # 4. 生成校验和
    checksum = hashlib.md5(json_str.encode()).hexdigest()[:8]
    print(f"\n🔍 步骤4: 生成校验和")
    print(f"MD5校验和: {checksum}")
    
    # 5. 组合数据
    combined = f"{encrypted}|{checksum}"
    print(f"\n🔗 步骤5: 组合数据")
    print(f"组合长度: {len(combined)}")
    print(f"格式: 加密数据|校验和")
    
    # 6. Base64编码
    encoded = base64.b64encode(combined.encode()).decode()
    print(f"\n📦 步骤6: Base64编码")
    print(f"编码长度: {len(encoded)}")
    print(f"编码内容: {encoded[:50]}...")
    
    # 7. 分段处理
    print(f"\n✂️ 步骤7: 分段处理")
    segments = []
    chunk_size = len(encoded) // 5
    remainder = len(encoded) % 5
    
    start = 0
    for i in range(5):
        end = start + chunk_size + (1 if i < remainder else 0)
        segment = encoded[start:end]
        
        # 确保每段都是4字符
        if len(segment) < 4:
            segment += 'A' * (4 - len(segment))
        elif len(segment) > 4:
            segment = segment[:4]
            
        segments.append(segment)
        start = end
        print(f"段{i+1}: {segment}")
    
    # 8. 最终卡密
    final_license = '-'.join(segments)
    print(f"\n🎫 步骤8: 最终卡密")
    print(f"卡密: {final_license}")
    print(f"格式: {'✅ 正确' if final_license.count('-') == 4 else '❌ 错误'}")
    print(f"长度: {len(final_license)}")
    
    return final_license

def test_license_system_integration():
    """测试与卡密系统的集成"""
    print("\n🔗 卡密系统集成测试")
    print("=" * 50)
    
    try:
        # 导入卡密系统
        import license_system.crypto_utils as crypto_utils
        
        print("✅ 成功导入卡密系统")
        
        # 测试生成卡密
        license_key = crypto_utils.LicenseGenerator.generate_license(
            user_id="TEST_USER_001",
            license_type="professional", 
            duration_days=365,
            features=["basic", "batch_collect", "export_data"],
            hardware_fingerprint="test_hardware_fp"
        )
        
        print(f"✅ 生成卡密成功: {license_key}")
        
        # 测试解密卡密
        decoded_data = crypto_utils.CryptoUtils.decode_license_data(license_key)
        print("✅ 解密卡密成功")
        print("📋 解密后的数据:")
        for key, value in decoded_data.items():
            print(f"   {key}: {value}")
            
        return True
        
    except ImportError as e:
        print(f"❌ 导入卡密系统失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 卡密加密功能验证")
    print("=" * 60)
    
    # 逐步测试加密过程
    license_key = test_encryption_step_by_step()
    
    # 测试系统集成
    integration_success = test_license_system_integration()
    
    print("\n📊 测试总结")
    print("=" * 50)
    print(f"🔐 加密流程测试: ✅ 完成")
    print(f"🔗 系统集成测试: {'✅ 成功' if integration_success else '❌ 失败'}")
    
    print(f"\n🎯 结论:")
    if integration_success:
        print("✅ 卡密系统正常工作，确实使用了加密功能！")
        print("🔐 每个生成的卡密都经过了完整的加密流程")
        print("📋 原始授权数据被安全地加密存储在卡密中")
    else:
        print("⚠️ 卡密系统可能存在问题，请检查配置")
    
    print(f"\n🔑 生成的测试卡密: {license_key}")

if __name__ == "__main__":
    main()
