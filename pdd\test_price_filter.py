#!/usr/bin/env python3
"""
测试价格筛选功能的简单脚本
"""
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from gui.main_window import MainWindow

def test_price_filter_ui():
    """测试价格筛选界面"""
    try:
        # 创建主窗口
        window = MainWindow()
        
        # 测试价格筛选设置
        print("测试价格筛选界面...")
        
        # 设置一些测试值
        window.price_filter_enabled_var.set(True)
        window.price_filter_mode.set("range")
        window.min_price_var.set("1.0")
        window.max_price_var.set("10.0")
        
        # 更新筛选状态
        window.update_filter_status()
        
        # 打印当前状态
        print(f"筛选状态: {window.filter_status_var.get()}")
        
        # 测试快速设置
        window.set_price_range(5, 20)
        print(f"快速设置后状态: {window.filter_status_var.get()}")
        
        print("价格筛选界面测试完成！")
        
        # 运行界面（可选）
        # window.run()
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_price_filter_ui()
