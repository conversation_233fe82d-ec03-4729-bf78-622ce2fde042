# 🔐 卡密系统使用说明

## 📋 概述

本项目实现了一个独立的卡密系统，支持开发版和发布版的分离构建。卡密系统只在发布版本中集成，不影响开发环境。

## 🏗️ 系统架构

### 目录结构
```
pdd/
├── core/                    # 核心功能
├── gui/                     # 界面组件
├── license_system/          # 🔐 独立卡密系统
│   ├── __init__.py         # 模块初始化
│   ├── license_manager.py  # 卡密管理核心
│   ├── license_interface.py # 统一接口
│   ├── crypto_utils.py     # 加密解密工具
│   ├── hardware_info.py    # 硬件信息获取
│   ├── ui_integration.py   # UI集成组件
│   ├── config.py          # 配置文件
│   └── exceptions.py       # 异常类
├── main.py                 # 主程序
├── build.py               # 打包脚本
└── auto_setup.py          # 自动构建脚本
```

## 🚀 快速开始

### 1. 开发环境（无卡密系统）
```bash
# 直接运行，无需卡密验证
python main.py

# 或设置环境变量
set BUILD_TYPE=dev
python main.py
```

### 2. 发布环境（集成卡密系统）
```bash
# 设置环境变量
set BUILD_TYPE=release
python main.py

# 或使用打包脚本
python auto_setup.py --type=release
```

## 🔧 构建说明

### 使用 auto_setup.py（推荐）
```bash
# 构建发布版本
python auto_setup.py --type=release

# 构建开发版本
python auto_setup.py --type=dev

# 构建两个版本
python auto_setup.py --type=both

# 跳过依赖安装
python auto_setup.py --type=release --skip-deps

# 跳过Chrome设置
python auto_setup.py --type=release --skip-chrome
```

### 使用 build.py
```bash
# 构建发布版本
python build.py --type=release

# 构建开发版本
python build.py --type=dev

# 构建exe文件
python build.py --type=release --exe
```

## 🔐 卡密系统功能

### 功能权限
- `basic` - 基础功能
- `batch_collect` - 批量采集
- `export_data` - 数据导出
- `advanced_filter` - 高级筛选
- `auto_retry` - 自动重试
- `multi_thread` - 多线程采集
- `schedule` - 定时采集
- `api_access` - API接口

### 授权类型
- **试用版** - 7天，基础功能
- **个人版** - 1年，基础+导出+筛选
- **专业版** - 1年，基础+导出+筛选+重试+多线程
- **企业版** - 1年，全功能

### 卡密格式
```
XXXX-XXXX-XXXX-XXXX-XXXX
```

## 💻 代码集成

### 1. 条件导入
```python
try:
    from license_system import LicenseInterface
    LICENSE_ENABLED = True
except ImportError:
    LICENSE_ENABLED = False
```

### 2. 功能验证装饰器
```python
from license_system import license_required

@license_required("batch_collect")
def batch_collect_function():
    # 批量采集功能
    pass
```

### 3. 手动验证
```python
from license_system import LicenseInterface

if LicenseInterface.check_feature("export_data"):
    # 执行导出功能
    pass
else:
    # 显示权限不足提示
    pass
```

### 4. UI集成
```python
from license_system.ui_integration import show_license_dialog, create_license_status_widget

# 显示授权对话框
if show_license_dialog(parent_window):
    print("授权成功")

# 创建状态显示组件
status_widget = create_license_status_widget(parent_frame)
status_widget.pack()
```

## 🛠️ 卡密生成器

### 生成试用卡密
```python
from license_system.crypto_utils import LicenseGenerator

trial_license = LicenseGenerator.generate_trial_license()
print(f"试用卡密: {trial_license}")
```

### 生成正式卡密
```python
license_key = LicenseGenerator.generate_license(
    user_id="USER001",
    license_type="professional",
    duration_days=365,
    features=["basic", "batch_collect", "export_data", "auto_retry"],
    hardware_fingerprint="optional_hardware_binding"
)
```

## 📦 打包配置

### 环境变量
- `BUILD_TYPE=dev` - 开发版本，不集成卡密系统
- `BUILD_TYPE=release` - 发布版本，集成卡密系统

### PyInstaller配置
发布版本会自动添加：
```python
--add-data license_system;license_system
--hidden-import license_system
--hidden-import cryptography
```

## 🔍 故障排除

### 1. 卡密系统未找到
```
ImportError: No module named 'license_system'
```
**解决方案：** 确保在发布版本中包含了 license_system 目录

### 2. 加密库缺失
```
ImportError: No module named 'cryptography'
```
**解决方案：** 安装加密依赖
```bash
pip install cryptography pycryptodome
```

### 3. 硬件绑定失败
```
LicenseHardwareError: 硬件绑定验证失败
```
**解决方案：** 检查硬件是否发生变化，或联系管理员重新绑定

### 4. 卡密格式错误
```
LicenseFormatError: 卡密格式不正确
```
**解决方案：** 检查卡密格式是否为 XXXX-XXXX-XXXX-XXXX-XXXX

## 📞 技术支持

### 开发相关
- 卡密系统完全独立，不影响主程序开发
- 开发时可以忽略 license_system 目录
- 所有功能在开发模式下默认可用

### 部署相关
- 发布版本必须包含完整的 license_system 目录
- 确保安装了所有加密依赖
- 建议在打包前测试卡密系统功能

### 安全相关
- 卡密使用AES-256加密
- 支持硬件绑定防止盗用
- 包含防篡改签名验证

## 🎯 最佳实践

1. **开发阶段** - 使用 `BUILD_TYPE=dev` 进行开发和调试
2. **测试阶段** - 使用 `BUILD_TYPE=release` 测试卡密功能
3. **发布阶段** - 使用 auto_setup.py 构建最终版本
4. **版本控制** - 可选择性提交 license_system 目录
5. **密钥管理** - 生产环境使用独立的密钥管理系统

## 📈 扩展功能

### 添加新功能权限
1. 在 `config.py` 中添加新功能定义
2. 在代码中使用 `@license_required("new_feature")` 装饰器
3. 更新卡密生成器以支持新功能

### 自定义授权类型
1. 在 `config.py` 中定义新的授权类型
2. 配置对应的功能权限和有效期
3. 更新UI显示相关信息

这个卡密系统设计灵活、安全、易于集成，完全满足商业化需求！🎉
