import sys
import os
sys.path.insert(0, '.')

# 写入文件而不是打印到控制台
def write_result(message):
    with open("test_output.txt", "a", encoding="utf-8") as f:
        f.write(message + "\n")

# 清空文件
with open("test_output.txt", "w", encoding="utf-8") as f:
    f.write("")

try:
    write_result("🔧 开始测试卡密系统")
    
    import license_system.crypto_utils as crypto
    write_result("✅ 模块导入成功")
    
    # 测试卡密生成
    license_key = crypto.LicenseGenerator.generate_license(
        user_id="TEST_USER",
        license_type="personal",
        duration_days=30,
        features=["basic"],
        hardware_fingerprint=None
    )
    
    write_result(f"✅ 卡密生成成功: {license_key}")
    write_result(f"📏 卡密长度: {len(license_key)}")
    write_result(f"🔗 连字符数量: {license_key.count('-')}")
    
    # 检查格式
    segments = license_key.split('-')
    write_result(f"📋 段数: {len(segments)}")
    for i, seg in enumerate(segments):
        write_result(f"   段{i+1}: '{seg}' (长度: {len(seg)})")
    
    # 测试解码
    decoded_data = crypto.CryptoUtils.decode_license_data(license_key)
    write_result("✅ 卡密解码成功")
    write_result("📋 解码后的数据:")
    for key, value in decoded_data.items():
        write_result(f"   {key}: {value}")
    
    write_result("🎉 所有测试通过！")
    
except Exception as e:
    write_result(f"❌ 测试失败: {e}")
    import traceback
    write_result(traceback.format_exc())

write_result("测试完成")
