"""
硬件信息获取模块
用于硬件绑定验证
"""
import hashlib
import platform
import subprocess
import uuid
import os
from typing import Dict, Optional


class HardwareInfo:
    """硬件信息获取类"""
    
    @staticmethod
    def get_mac_address() -> str:
        """获取MAC地址"""
        try:
            mac = uuid.getnode()
            return ':'.join(('%012X' % mac)[i:i+2] for i in range(0, 12, 2))
        except:
            return "00:00:00:00:00:00"
    
    @staticmethod
    def get_cpu_info() -> str:
        """获取CPU信息"""
        try:
            if platform.system() == "Windows":
                result = subprocess.run(
                    ['wmic', 'cpu', 'get', 'ProcessorId', '/value'],
                    capture_output=True, text=True, timeout=10
                )
                for line in result.stdout.split('\n'):
                    if 'ProcessorId=' in line:
                        return line.split('=')[1].strip()
            elif platform.system() == "Linux":
                with open('/proc/cpuinfo', 'r') as f:
                    for line in f:
                        if 'Serial' in line:
                            return line.split(':')[1].strip()
            elif platform.system() == "Darwin":  # macOS
                result = subprocess.run(
                    ['system_profiler', 'SPHardwareDataType'],
                    capture_output=True, text=True, timeout=10
                )
                for line in result.stdout.split('\n'):
                    if 'Serial Number' in line:
                        return line.split(':')[1].strip()
        except:
            pass
        return platform.processor()
    
    @staticmethod
    def get_motherboard_serial() -> str:
        """获取主板序列号"""
        try:
            if platform.system() == "Windows":
                result = subprocess.run(
                    ['wmic', 'baseboard', 'get', 'SerialNumber', '/value'],
                    capture_output=True, text=True, timeout=10
                )
                for line in result.stdout.split('\n'):
                    if 'SerialNumber=' in line:
                        return line.split('=')[1].strip()
            elif platform.system() == "Linux":
                result = subprocess.run(
                    ['dmidecode', '-s', 'baseboard-serial-number'],
                    capture_output=True, text=True, timeout=10
                )
                return result.stdout.strip()
        except:
            pass
        return "UNKNOWN"
    
    @staticmethod
    def get_disk_serial() -> str:
        """获取硬盘序列号"""
        try:
            if platform.system() == "Windows":
                result = subprocess.run(
                    ['wmic', 'diskdrive', 'get', 'SerialNumber', '/value'],
                    capture_output=True, text=True, timeout=10
                )
                for line in result.stdout.split('\n'):
                    if 'SerialNumber=' in line and line.split('=')[1].strip():
                        return line.split('=')[1].strip()
            elif platform.system() == "Linux":
                result = subprocess.run(
                    ['lsblk', '-d', '-o', 'SERIAL'],
                    capture_output=True, text=True, timeout=10
                )
                lines = result.stdout.strip().split('\n')
                if len(lines) > 1:
                    return lines[1].strip()
        except:
            pass
        return "UNKNOWN"
    
    @classmethod
    def get_hardware_fingerprint(cls) -> str:
        """生成硬件指纹"""
        info = {
            'mac': cls.get_mac_address(),
            'cpu': cls.get_cpu_info(),
            'motherboard': cls.get_motherboard_serial(),
            'disk': cls.get_disk_serial(),
            'platform': platform.platform(),
            'machine': platform.machine()
        }
        
        # 组合所有硬件信息
        combined = '|'.join([f"{k}:{v}" for k, v in sorted(info.items())])
        
        # 生成SHA256哈希
        return hashlib.sha256(combined.encode('utf-8')).hexdigest()[:32]
    
    @classmethod
    def get_hardware_info(cls) -> Dict[str, str]:
        """获取详细硬件信息"""
        return {
            'fingerprint': cls.get_hardware_fingerprint(),
            'mac_address': cls.get_mac_address(),
            'cpu_info': cls.get_cpu_info(),
            'motherboard_serial': cls.get_motherboard_serial(),
            'disk_serial': cls.get_disk_serial(),
            'platform': platform.platform(),
            'machine': platform.machine(),
            'system': platform.system(),
            'release': platform.release(),
            'version': platform.version()
        }
    
    @classmethod
    def save_hardware_info(cls, filepath: str) -> bool:
        """保存硬件信息到文件"""
        try:
            import json
            info = cls.get_hardware_info()
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(info, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存硬件信息失败: {e}")
            return False
    
    @classmethod
    def load_hardware_info(cls, filepath: str) -> Optional[Dict[str, str]]:
        """从文件加载硬件信息"""
        try:
            import json
            if os.path.exists(filepath):
                with open(filepath, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"加载硬件信息失败: {e}")
        return None
    
    @classmethod
    def verify_hardware_binding(cls, saved_fingerprint: str, tolerance: float = 0.8) -> bool:
        """验证硬件绑定"""
        current_fingerprint = cls.get_hardware_fingerprint()
        
        # 完全匹配
        if current_fingerprint == saved_fingerprint:
            return True
        
        # 容错匹配 (考虑硬件可能的微小变化)
        if len(current_fingerprint) == len(saved_fingerprint):
            matches = sum(1 for a, b in zip(current_fingerprint, saved_fingerprint) if a == b)
            similarity = matches / len(current_fingerprint)
            return similarity >= tolerance
        
        return False
