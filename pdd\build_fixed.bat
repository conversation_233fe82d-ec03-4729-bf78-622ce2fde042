@echo off
chcp 65001 >nul
echo [INFO] Building PDD Spider with enhanced imports...

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python not found
    pause
    exit /b 1
)

REM 检查DrissionPage
python -c "import DrissionPage" >nul 2>&1
if errorlevel 1 (
    echo [ERROR] DrissionPage not found
    echo Please install: pip install DrissionPage
    pause
    exit /b 1
)

REM 检查PyInstaller
python -c "import PyInstaller" >nul 2>&1
if errorlevel 1 (
    echo [INFO] Installing PyInstaller...
    pip install pyinstaller
)

echo [OK] Environment ready

REM 清理旧文件
if exist "dist" rmdir /s /q "dist" 2>nul
if exist "build" rmdir /s /q "build" 2>nul
if exist "*.spec" del /q "*.spec" 2>nul

echo [INFO] Starting enhanced build...

REM 增强版打包命令 - 使用单行避免分割问题
python -m PyInstaller --onefile --windowed --name "<PERSON><PERSON>_Spider" --add-data "Chrome;Chrome" --hidden-import=DrissionPage --hidden-import=DrissionPage.common --collect-submodules=DrissionPage --collect-data=DrissionPage --hidden-import=tkinter --hidden-import=requests --hidden-import=json --hidden-import=threading main.py

if errorlevel 1 (
    echo [ERROR] Build failed!
    echo Check the error messages above
    pause
    exit /b 1
)

echo.
echo [SUCCESS] Build completed!
if exist "dist\PDD_Spider.exe" (
    echo [INFO] File: dist\PDD_Spider.exe
    for %%I in ("dist\PDD_Spider.exe") do echo [INFO] Size: %%~zI bytes
) else (
    echo [ERROR] Executable not found!
)

echo.
echo [INFO] Testing the executable...
echo [INFO] You can now test: dist\PDD_Spider.exe
pause
