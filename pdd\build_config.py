#!/usr/bin/env python3
"""
构建配置管理器
用于在打包前选择不同的版本配置
"""
import os
import json
import shutil
from pathlib import Path

class BuildConfig:
    """构建配置管理器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.config_dir = self.project_root / "build_configs"
        self.config_dir.mkdir(exist_ok=True)
        
        # 确保配置文件存在
        self.ensure_config_files()
    
    def ensure_config_files(self):
        """确保配置文件存在"""
        configs = {
            "release": {
                "version": "1.0.0",
                "debug": False,
                "trial_mode": False,
                "verification": {
                    "time_check": True,
                    "hardware_binding": True,
                    "check_interval": 3600
                },
                "license_types": {
                    "trial": {
                        "name": "试用版",
                        "duration": 7,
                        "features": ["basic", "batch_collect"]
                    },
                    "personal": {
                        "name": "个人版", 
                        "duration": 365,
                        "features": ["basic", "batch_collect", "export_data", "advanced_filter"]
                    },
                    "professional": {
                        "name": "专业版",
                        "duration": 365,
                        "features": ["basic", "batch_collect", "export_data", "advanced_filter", "auto_retry", "multi_thread"]
                    },
                    "enterprise": {
                        "name": "企业版",
                        "duration": 365,
                        "features": ["basic", "batch_collect", "export_data", "advanced_filter", "auto_retry", "multi_thread", "schedule", "api_access"]
                    }
                },
                "encryption_key": b"PDD_SPIDER_LICENSE_KEY_2024_SECURE",
                "database": {
                    "name": "spider_data.db",
                    "backup_enabled": True
                },
                "logging": {
                    "level": "INFO",
                    "file_enabled": True,
                    "console_enabled": False
                },
                "ui": {
                    "show_debug_info": False,
                    "enable_dev_tools": False
                }
            },
            "dev": {
                "version": "1.0.0-dev",
                "debug": True,
                "trial_mode": True,
                "verification": {
                    "time_check": False,
                    "hardware_binding": False,
                    "check_interval": 60
                },
                "license_types": {
                    "trial": {
                        "name": "试用版",
                        "duration": 30,
                        "features": ["basic", "batch_collect"]
                    },
                    "personal": {
                        "name": "个人版",
                        "duration": 365,
                        "features": ["basic", "batch_collect", "export_data", "advanced_filter"]
                    },
                    "professional": {
                        "name": "专业版",
                        "duration": 365,
                        "features": ["basic", "batch_collect", "export_data", "advanced_filter", "auto_retry", "multi_thread"]
                    },
                    "enterprise": {
                        "name": "企业版",
                        "duration": 365,
                        "features": ["basic", "batch_collect", "export_data", "advanced_filter", "auto_retry", "multi_thread", "schedule", "api_access"]
                    },
                    "dev": {
                        "name": "开发版",
                        "duration": 9999,
                        "features": ["basic", "batch_collect", "export_data", "advanced_filter", "auto_retry", "multi_thread", "schedule", "api_access", "debug"]
                    }
                },
                "encryption_key": b"PDD_SPIDER_DEV_KEY_2024_DEBUG_MODE",
                "database": {
                    "name": "spider_data_dev.db",
                    "backup_enabled": False
                },
                "logging": {
                    "level": "DEBUG",
                    "file_enabled": True,
                    "console_enabled": True
                },
                "ui": {
                    "show_debug_info": True,
                    "enable_dev_tools": True
                }
            }
        }
        
        for config_name, config_data in configs.items():
            config_file = self.config_dir / f"{config_name}.json"
            if not config_file.exists():
                with open(config_file, 'w', encoding='utf-8') as f:
                    # 处理bytes类型的序列化
                    config_copy = config_data.copy()
                    config_copy["encryption_key"] = config_data["encryption_key"].decode('utf-8')
                    json.dump(config_copy, f, indent=2, ensure_ascii=False)
    
    def get_available_configs(self):
        """获取可用的配置列表"""
        configs = []
        for config_file in self.config_dir.glob("*.json"):
            configs.append(config_file.stem)
        return sorted(configs)
    
    def load_config(self, config_name):
        """加载指定配置"""
        config_file = self.config_dir / f"{config_name}.json"
        if not config_file.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_file}")
        
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 处理bytes类型的反序列化
        if "encryption_key" in config:
            config["encryption_key"] = config["encryption_key"].encode('utf-8')
        
        return config
    
    def apply_config(self, config_name):
        """应用指定配置到项目"""
        print(f"🔧 应用配置: {config_name}")
        
        config = self.load_config(config_name)
        
        # 1. 更新license_system/config.py
        self.update_license_config(config)
        
        # 2. 更新主程序配置
        self.update_main_config(config)
        
        # 3. 创建版本标识文件
        self.create_version_file(config_name, config)
        
        print(f"✅ 配置 {config_name} 应用完成")
    
    def update_license_config(self, config):
        """更新卡密系统配置"""
        license_config_file = self.project_root / "license_system" / "config.py"
        
        # 生成新的配置内容
        config_content = f'''"""
卡密系统配置文件
构建版本: {config["version"]}
调试模式: {config["debug"]}
"""

# 版本信息
VERSION = "{config["version"]}"
DEBUG_MODE = {config["debug"]}
TRIAL_MODE = {config["trial_mode"]}

# 加密配置
ENCRYPTION_KEY = {repr(config["encryption_key"])}
RSA_KEY_SIZE = 2048

# 卡密格式配置
LICENSE_FORMAT = "XXXX-XXXX-XXXX-XXXX-XXXX"
LICENSE_SEGMENTS = 5
SEGMENT_LENGTH = 4

# 功能权限定义
FEATURES = {{
    "basic": "基础功能",
    "batch_collect": "批量采集",
    "export_data": "数据导出",
    "advanced_filter": "高级筛选",
    "auto_retry": "自动重试",
    "multi_thread": "多线程采集",
    "schedule": "定时采集",
    "api_access": "API接口",
    "debug": "调试功能"
}}

# 授权类型配置
LICENSE_TYPES = {json.dumps(config["license_types"], indent=4, ensure_ascii=False)}

# 验证配置
VERIFICATION = {json.dumps(config["verification"], indent=4, ensure_ascii=False)}

# 文件路径配置
LICENSE_FILE = "license.lic"
HARDWARE_FILE = "hardware.dat"
PUBLIC_KEY = ""
'''
        
        # 写入配置文件
        with open(license_config_file, 'w', encoding='utf-8') as f:
            f.write(config_content)
        
        print(f"✅ 更新卡密系统配置")
    
    def update_main_config(self, config):
        """更新主程序配置"""
        # 创建主程序配置文件
        main_config_file = self.project_root / "app_config.py"
        
        config_content = f'''"""
主程序配置文件
构建版本: {config["version"]}
"""

# 应用信息
APP_VERSION = "{config["version"]}"
APP_NAME = "拼多多采集工具"
DEBUG_MODE = {config["debug"]}

# 数据库配置
DATABASE = {json.dumps(config["database"], indent=4, ensure_ascii=False)}

# 日志配置
LOGGING = {json.dumps(config["logging"], indent=4, ensure_ascii=False)}

# UI配置
UI = {json.dumps(config["ui"], indent=4, ensure_ascii=False)}
'''
        
        with open(main_config_file, 'w', encoding='utf-8') as f:
            f.write(config_content)
        
        print(f"✅ 更新主程序配置")
    
    def create_version_file(self, config_name, config):
        """创建版本标识文件"""
        version_file = self.project_root / "VERSION"
        
        version_content = f'''构建版本: {config["version"]}
构建配置: {config_name}
构建时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
调试模式: {config["debug"]}
试用模式: {config["trial_mode"]}
'''
        
        with open(version_file, 'w', encoding='utf-8') as f:
            f.write(version_content)
        
        print(f"✅ 创建版本文件")
    
    def interactive_select(self):
        """交互式选择配置"""
        configs = self.get_available_configs()
        
        print("🔧 构建配置选择器")
        print("=" * 50)
        print("可用的配置:")
        
        for i, config in enumerate(configs, 1):
            try:
                config_data = self.load_config(config)
                print(f"  {i}. {config} - {config_data['version']} ({'调试' if config_data['debug'] else '发布'})")
            except:
                print(f"  {i}. {config} - 配置文件损坏")
        
        while True:
            try:
                choice = input(f"\n请选择配置 (1-{len(configs)}): ").strip()
                if not choice:
                    continue
                
                index = int(choice) - 1
                if 0 <= index < len(configs):
                    selected_config = configs[index]
                    
                    # 显示配置详情
                    config_data = self.load_config(selected_config)
                    print(f"\n📋 选择的配置: {selected_config}")
                    print(f"版本: {config_data['version']}")
                    print(f"调试模式: {config_data['debug']}")
                    print(f"试用模式: {config_data['trial_mode']}")
                    print(f"硬件绑定: {config_data['verification']['hardware_binding']}")
                    
                    confirm = input("\n确认应用此配置? (y/N): ").strip().lower()
                    if confirm in ['y', 'yes']:
                        self.apply_config(selected_config)
                        return selected_config
                    else:
                        continue
                else:
                    print("❌ 无效的选择")
            except ValueError:
                print("❌ 请输入数字")
            except KeyboardInterrupt:
                print("\n❌ 操作取消")
                return None


def main():
    """主函数"""
    import sys
    
    builder = BuildConfig()
    
    if len(sys.argv) > 1:
        # 命令行指定配置
        config_name = sys.argv[1]
        try:
            builder.apply_config(config_name)
            print(f"🎉 配置 {config_name} 应用成功")
        except Exception as e:
            print(f"❌ 应用配置失败: {e}")
            sys.exit(1)
    else:
        # 交互式选择
        selected = builder.interactive_select()
        if selected:
            print(f"🎉 配置 {selected} 应用成功")
            print("\n现在可以进行打包操作")
        else:
            print("❌ 未选择配置")
            sys.exit(1)


if __name__ == "__main__":
    main()
