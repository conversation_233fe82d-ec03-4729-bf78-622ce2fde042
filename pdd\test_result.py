import sys
import os
sys.path.insert(0, '.')

result_file = "test_result.txt"

try:
    with open(result_file, "w", encoding="utf-8") as f:
        f.write("开始测试\n")
        
        import license_system.crypto_utils as crypto
        f.write("导入成功\n")
        
        key = crypto.LicenseGenerator.generate_license(
            user_id="TEST",
            license_type="personal",
            duration_days=30,
            features=["basic"],
            hardware_fingerprint=None
        )
        f.write(f"卡密: {key}\n")
        
        data = crypto.CryptoUtils.decode_license_data(key)
        f.write(f"用户ID: {data['user_id']}\n")
        
        f.write("测试成功！\n")
        
except Exception as e:
    with open(result_file, "w", encoding="utf-8") as f:
        f.write(f"错误: {e}\n")
        import traceback
        f.write(traceback.format_exc())

print("测试完成，结果已写入文件")
