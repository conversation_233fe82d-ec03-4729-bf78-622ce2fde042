#!/usr/bin/env python3
import sys
import os

print("🔍 调试测试开始")
print(f"Python版本: {sys.version}")
print(f"当前目录: {os.getcwd()}")

# 添加路径
sys.path.insert(0, '.')
print(f"Python路径: {sys.path[:3]}")

# 检查license_system目录
license_dir = os.path.join('.', 'license_system')
print(f"license_system目录存在: {os.path.exists(license_dir)}")

if os.path.exists(license_dir):
    files = os.listdir(license_dir)
    print(f"license_system文件: {files}")

# 尝试导入
try:
    print("\n📦 尝试导入模块...")
    import license_system
    print("✅ license_system导入成功")
    
    import license_system.config
    print("✅ config导入成功")
    
    import license_system.exceptions
    print("✅ exceptions导入成功")
    
    import license_system.crypto_utils
    print("✅ crypto_utils导入成功")
    
    # 测试加密
    print("\n🔐 测试加密功能...")
    crypto = license_system.crypto_utils
    
    # 检查加密库
    print(f"CRYPTO_AVAILABLE: {crypto.CRYPTO_AVAILABLE}")
    
    # 测试基础加密
    test_data = "Hello World"
    encrypted = crypto.CryptoUtils.encrypt_data(test_data)
    print(f"✅ 加密成功: {len(encrypted)} 字符")
    
    decrypted = crypto.CryptoUtils.decrypt_data(encrypted)
    print(f"✅ 解密成功: {decrypted}")
    
    if test_data == decrypted:
        print("✅ 加密解密一致")
    else:
        print("❌ 加密解密不一致")
    
    # 测试卡密生成
    print("\n🎫 测试卡密生成...")
    license_key = crypto.LicenseGenerator.generate_license(
        user_id="DEBUG_TEST",
        license_type="personal",
        duration_days=30,
        features=["basic"],
        hardware_fingerprint=None
    )
    
    print(f"✅ 卡密生成成功")
    print(f"🔑 卡密: {license_key}")
    print(f"📏 长度: {len(license_key)}")
    print(f"🔗 格式: {license_key.count('-')} 个连字符")
    
    # 测试解密
    print("\n🔓 测试卡密解密...")
    decoded = crypto.CryptoUtils.decode_license_data(license_key)
    print(f"✅ 解密成功")
    print(f"👤 用户ID: {decoded.get('user_id')}")
    print(f"🏷️ 类型: {decoded.get('license_type')}")
    print(f"🔧 功能: {decoded.get('features')}")
    
    print("\n🎉 所有测试通过！")
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    import traceback
    traceback.print_exc()
    
except Exception as e:
    print(f"❌ 运行错误: {e}")
    import traceback
    traceback.print_exc()

print("\n🔍 调试测试完成")
