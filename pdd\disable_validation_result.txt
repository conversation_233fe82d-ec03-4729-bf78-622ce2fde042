🧪 测试卡密禁用验证功能
==================================================
✅ 测试数据库创建完成

📋 数据库中的卡密状态:
  用户: ACTIVE_USER, 卡密: ACTIVE-TEST-1111-2222-3333, 状态: ✅ 正常
  用户: DISABLED_USER, 卡密: DISABLED-TEST-4444-5555-6666, 状态: 🚫 已禁用

🔍 测试状态检查函数:
正常卡密状态: active
禁用卡密状态: disabled

🔐 测试卡密验证逻辑:
正常卡密验证: ✅ 通过 - 卡密正常
禁用卡密验证: ❌ 被拒绝 - 卡密已被禁用

🚫 测试禁用操作:
✅ 已禁用 ACTIVE_USER 的卡密
禁用后状态: disabled
禁用后验证: ❌ 被拒绝 - 卡密已被禁用

✅ 测试启用操作:
✅ 已启用 DISABLED_USER 的卡密
启用后状态: active
启用后验证: ✅ 通过 - 卡密正常

🎉 禁用验证功能测试完成！
✅ 状态检查功能正常
✅ 禁用卡密被正确拒绝
✅ 启用卡密可以正常使用
测试完成
