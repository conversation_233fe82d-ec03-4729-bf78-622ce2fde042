#!/usr/bin/env python3
"""
测试卡密禁用功能
"""
import sys
import os
import sqlite3
from datetime import datetime, timedelta

sys.path.insert(0, '.')

def test_disable_function():
    """测试禁用功能"""
    print("🧪 测试卡密禁用功能")
    print("=" * 50)
    
    # 创建测试数据库
    test_db = "test_license_data.db"
    if os.path.exists(test_db):
        os.remove(test_db)
    
    conn = sqlite3.connect(test_db)
    
    try:
        # 创建表结构
        conn.execute('''
            CREATE TABLE IF NOT EXISTS licenses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_id INTEGER,
                license_key TEXT NOT NULL,
                license_type TEXT NOT NULL,
                user_id TEXT NOT NULL,
                features TEXT,
                issue_date DATE,
                expire_date DATE,
                hardware_fingerprint TEXT,
                status TEXT DEFAULT 'active',
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 插入测试数据
        test_licenses = [
            ("TEST-AAAA-BBBB-CCCC-DDDD", "personal", "USER001", "active"),
            ("TEST-EEEE-FFFF-GGGG-HHHH", "professional", "USER002", "active"),
            ("TEST-IIII-JJJJ-KKKK-LLLL", "enterprise", "USER003", "disabled"),
        ]
        
        for license_key, license_type, user_id, status in test_licenses:
            conn.execute('''
                INSERT INTO licenses (license_key, license_type, user_id, status, 
                                    issue_date, expire_date, features)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                license_key, license_type, user_id, status,
                datetime.now().date(),
                (datetime.now() + timedelta(days=365)).date(),
                '["basic"]'
            ))
        
        conn.commit()
        print("✅ 测试数据创建成功")
        
        # 查询初始状态
        print("\n📋 初始卡密状态:")
        cursor = conn.execute("SELECT id, license_key, status FROM licenses ORDER BY id")
        for row in cursor.fetchall():
            print(f"  ID: {row[0]}, 卡密: {row[1]}, 状态: {row[2]}")
        
        # 测试禁用功能
        print("\n🚫 测试禁用功能...")
        license_id = 1  # 禁用第一个卡密
        
        # 执行禁用操作
        conn.execute("UPDATE licenses SET status = 'disabled' WHERE id = ?", (license_id,))
        conn.commit()
        print(f"✅ 卡密 ID {license_id} 已禁用")
        
        # 查询禁用后状态
        print("\n📋 禁用后卡密状态:")
        cursor = conn.execute("SELECT id, license_key, status FROM licenses ORDER BY id")
        for row in cursor.fetchall():
            status_display = "🚫 已禁用" if row[2] == 'disabled' else "✅ 正常"
            print(f"  ID: {row[0]}, 卡密: {row[1]}, 状态: {status_display}")
        
        # 测试启用功能
        print("\n✅ 测试启用功能...")
        license_id = 3  # 启用第三个卡密（原本是禁用的）
        
        # 执行启用操作
        conn.execute("UPDATE licenses SET status = 'active' WHERE id = ?", (license_id,))
        conn.commit()
        print(f"✅ 卡密 ID {license_id} 已启用")
        
        # 查询启用后状态
        print("\n📋 启用后卡密状态:")
        cursor = conn.execute("SELECT id, license_key, status FROM licenses ORDER BY id")
        for row in cursor.fetchall():
            status_display = "🚫 已禁用" if row[2] == 'disabled' else "✅ 正常"
            print(f"  ID: {row[0]}, 卡密: {row[1]}, 状态: {status_display}")
        
        # 统计状态
        print("\n📊 状态统计:")
        cursor = conn.execute("SELECT status, COUNT(*) FROM licenses GROUP BY status")
        for row in cursor.fetchall():
            status_name = "已禁用" if row[0] == 'disabled' else "正常"
            print(f"  {status_name}: {row[1]} 个")
        
        print("\n🎉 禁用功能测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        conn.close()
        # 清理测试文件
        if os.path.exists(test_db):
            os.remove(test_db)

def test_status_display():
    """测试状态显示逻辑"""
    print("\n🎨 测试状态显示逻辑")
    print("=" * 50)
    
    # 模拟不同状态的显示
    test_statuses = [
        ('active', '✅ 正常'),
        ('disabled', '🚫 已禁用'),
        ('expired', '⏰ 已过期'),
        ('unknown', '❓ unknown')
    ]
    
    print("状态映射测试:")
    for status, expected_display in test_statuses:
        if status == 'disabled':
            status_display = '🚫 已禁用'
        elif status == 'expired':
            status_display = '⏰ 已过期'
        elif status == 'active':
            status_display = '✅ 正常'
        else:
            status_display = f'❓ {status}'
        
        result = "✅" if status_display == expected_display else "❌"
        print(f"  {status} → {status_display} {result}")
    
    print("✅ 状态显示逻辑测试完成")

def main():
    """主函数"""
    print("🔧 卡密禁用功能完整测试")
    print("=" * 60)
    
    # 测试数据库操作
    db_test_ok = test_disable_function()
    
    # 测试状态显示
    test_status_display()
    
    print("\n📊 测试总结")
    print("=" * 50)
    print(f"🗄️ 数据库操作测试: {'✅ 通过' if db_test_ok else '❌ 失败'}")
    print(f"🎨 状态显示测试: ✅ 通过")
    
    if db_test_ok:
        print("\n🎉 禁用功能测试全部通过！")
        print("✅ 禁用操作正常工作")
        print("✅ 启用操作正常工作") 
        print("✅ 状态显示正确")
        print("✅ 数据库更新成功")
    else:
        print("\n❌ 部分测试失败，请检查错误信息")

if __name__ == "__main__":
    main()
