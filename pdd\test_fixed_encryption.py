#!/usr/bin/env python3
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_encryption_fix():
    print("🔧 测试修复后的加密功能")
    print("=" * 50)
    
    try:
        import license_system.crypto_utils as crypto
        print("✅ 导入卡密系统成功")
        
        # 测试加密密钥
        from license_system.config import ENCRYPTION_KEY
        print(f"🔑 原始密钥长度: {len(ENCRYPTION_KEY)} 字节")
        print(f"🔑 原始密钥内容: {ENCRYPTION_KEY}")
        
        # 测试密钥处理
        import hashlib
        if len(ENCRYPTION_KEY) != 32:
            processed_key = hashlib.sha256(ENCRYPTION_KEY).digest()
            print(f"🔧 处理后密钥长度: {len(processed_key)} 字节")
        else:
            processed_key = ENCRYPTION_KEY
            print("✅ 密钥长度正确，无需处理")
        
        # 测试基础加密解密
        print("\n🔐 测试基础加密解密...")
        test_data = "Hello, License System!"
        
        encrypted = crypto.CryptoUtils.encrypt_data(test_data)
        print(f"✅ 加密成功: {encrypted[:50]}...")
        
        decrypted = crypto.CryptoUtils.decrypt_data(encrypted)
        print(f"✅ 解密成功: {decrypted}")
        
        if test_data == decrypted:
            print("✅ 加密解密一致性验证通过")
        else:
            print("❌ 加密解密一致性验证失败")
            return False
        
        # 测试卡密生成
        print("\n🎫 测试卡密生成...")
        license_key = crypto.LicenseGenerator.generate_license(
            user_id="TEST_USER_001",
            license_type="professional",
            duration_days=365,
            features=["basic", "batch_collect", "export_data"],
            hardware_fingerprint="test_hardware_fp"
        )
        
        print(f"✅ 卡密生成成功: {license_key}")
        print(f"📏 卡密长度: {len(license_key)}")
        print(f"🔗 卡密格式: {'✅ 正确' if license_key.count('-') == 4 else '❌ 错误'}")
        
        # 测试卡密解密
        print("\n🔓 测试卡密解密...")
        decoded_data = crypto.CryptoUtils.decode_license_data(license_key)
        print("✅ 卡密解密成功")
        print("📋 解密后的数据:")
        for key, value in decoded_data.items():
            print(f"   {key}: {value}")
        
        # 测试多次生成的唯一性
        print("\n🔄 测试卡密唯一性...")
        license_key2 = crypto.LicenseGenerator.generate_license(
            user_id="TEST_USER_001",
            license_type="professional",
            duration_days=365,
            features=["basic", "batch_collect", "export_data"],
            hardware_fingerprint="test_hardware_fp"
        )
        
        print(f"🎫 第二个卡密: {license_key2}")
        
        if license_key != license_key2:
            print("✅ 卡密唯一性验证通过（每次生成都不同）")
        else:
            print("⚠️ 卡密相同（可能是时间戳精度问题）")
        
        print("\n🎉 所有测试通过！加密功能正常工作！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cryptography_availability():
    print("\n🔧 测试加密库可用性")
    print("=" * 50)
    
    try:
        from cryptography.fernet import Fernet
        print("✅ cryptography库可用 - 使用AES-256加密")
        
        # 测试Fernet
        import base64
        test_key = b"test_key_32_bytes_long_for_fernet"
        fernet_key = base64.urlsafe_b64encode(test_key)
        fernet = Fernet(fernet_key)
        
        test_msg = "Test message"
        encrypted = fernet.encrypt(test_msg.encode())
        decrypted = fernet.decrypt(encrypted).decode()
        
        if test_msg == decrypted:
            print("✅ Fernet加密测试通过")
        else:
            print("❌ Fernet加密测试失败")
            
    except ImportError:
        print("⚠️ cryptography库不可用 - 使用Base64降级方案")
        
        # 测试Base64降级
        import base64
        test_msg = "Test message"
        encoded = base64.b64encode(test_msg.encode()).decode()
        decoded = base64.b64decode(encoded.encode()).decode()
        
        if test_msg == decoded:
            print("✅ Base64降级方案测试通过")
        else:
            print("❌ Base64降级方案测试失败")

if __name__ == "__main__":
    success = test_encryption_fix()
    test_cryptography_availability()
    
    if success:
        print("\n🎉 修复成功！卡密加密功能正常工作！")
    else:
        print("\n❌ 仍有问题，请检查错误信息")
