#!/usr/bin/env python3
"""
简单测试禁用验证功能
"""
import sys
import os
import sqlite3
from datetime import datetime, timedelta

sys.path.insert(0, '.')

def write_result(message):
    with open("disable_validation_result.txt", "a", encoding="utf-8") as f:
        f.write(message + "\n")

# 清空结果文件
with open("disable_validation_result.txt", "w", encoding="utf-8") as f:
    f.write("")

try:
    write_result("🧪 测试卡密禁用验证功能")
    write_result("=" * 50)
    
    # 1. 创建测试数据库
    db_path = "license_data.db"
    
    # 备份现有数据库
    if os.path.exists(db_path):
        import shutil
        shutil.copy2(db_path, "license_data_backup.db")
        write_result("✅ 已备份原数据库")
    
    conn = sqlite3.connect(db_path)
    
    # 创建表结构
    conn.execute('''
        CREATE TABLE IF NOT EXISTS licenses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            customer_id INTEGER,
            license_key TEXT NOT NULL,
            license_type TEXT NOT NULL,
            user_id TEXT NOT NULL,
            features TEXT,
            issue_date DATE,
            expire_date DATE,
            hardware_fingerprint TEXT,
            status TEXT DEFAULT 'active',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 清空现有数据并插入测试数据
    conn.execute("DELETE FROM licenses")
    
    test_licenses = [
        ("ACTIVE-TEST-1111-2222-3333", "personal", "ACTIVE_USER", "active"),
        ("DISABLED-TEST-4444-5555-6666", "professional", "DISABLED_USER", "disabled"),
    ]
    
    for license_key, license_type, user_id, status in test_licenses:
        conn.execute('''
            INSERT INTO licenses (license_key, license_type, user_id, status, 
                                issue_date, expire_date, features)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            license_key, license_type, user_id, status,
            datetime.now().date(),
            (datetime.now() + timedelta(days=365)).date(),
            '["basic"]'
        ))
    
    conn.commit()
    write_result("✅ 测试数据库创建完成")
    
    # 2. 查询数据库状态
    write_result("\n📋 数据库中的卡密状态:")
    cursor = conn.execute("SELECT user_id, license_key, status FROM licenses")
    for row in cursor.fetchall():
        status_display = "🚫 已禁用" if row[2] == 'disabled' else "✅ 正常"
        write_result(f"  用户: {row[0]}, 卡密: {row[1]}, 状态: {status_display}")
    
    # 3. 测试状态检查函数
    write_result("\n🔍 测试状态检查函数:")
    
    def check_license_status(user_id, license_key):
        """检查卡密状态"""
        try:
            cursor = conn.execute(
                "SELECT status FROM licenses WHERE user_id = ? OR license_key = ?",
                (user_id, license_key)
            )
            row = cursor.fetchone()
            if row:
                return row[0]
            else:
                return 'unknown'
        except Exception as e:
            return 'error'
    
    # 测试正常卡密
    active_status = check_license_status("ACTIVE_USER", "ACTIVE-TEST-1111-2222-3333")
    write_result(f"正常卡密状态: {active_status}")
    
    # 测试禁用卡密
    disabled_status = check_license_status("DISABLED_USER", "DISABLED-TEST-4444-5555-6666")
    write_result(f"禁用卡密状态: {disabled_status}")
    
    # 4. 测试卡密验证逻辑
    write_result("\n🔐 测试卡密验证逻辑:")
    
    def validate_license_with_status(user_id, license_key):
        """验证卡密（包含状态检查）"""
        status = check_license_status(user_id, license_key)
        
        if status == 'disabled':
            return False, "卡密已被禁用"
        elif status == 'active':
            return True, "卡密正常"
        else:
            return False, f"未知状态: {status}"
    
    # 验证正常卡密
    active_valid, active_msg = validate_license_with_status("ACTIVE_USER", "ACTIVE-TEST-1111-2222-3333")
    write_result(f"正常卡密验证: {'✅ 通过' if active_valid else '❌ 失败'} - {active_msg}")
    
    # 验证禁用卡密
    disabled_valid, disabled_msg = validate_license_with_status("DISABLED_USER", "DISABLED-TEST-4444-5555-6666")
    write_result(f"禁用卡密验证: {'❌ 被拒绝' if not disabled_valid else '⚠️ 意外通过'} - {disabled_msg}")
    
    # 5. 测试禁用操作
    write_result("\n🚫 测试禁用操作:")
    
    # 禁用一个正常卡密
    conn.execute("UPDATE licenses SET status = 'disabled' WHERE user_id = ?", ("ACTIVE_USER",))
    conn.commit()
    write_result("✅ 已禁用 ACTIVE_USER 的卡密")
    
    # 重新验证
    new_status = check_license_status("ACTIVE_USER", "ACTIVE-TEST-1111-2222-3333")
    write_result(f"禁用后状态: {new_status}")
    
    new_valid, new_msg = validate_license_with_status("ACTIVE_USER", "ACTIVE-TEST-1111-2222-3333")
    write_result(f"禁用后验证: {'❌ 被拒绝' if not new_valid else '⚠️ 意外通过'} - {new_msg}")
    
    # 6. 测试启用操作
    write_result("\n✅ 测试启用操作:")
    
    # 启用一个禁用的卡密
    conn.execute("UPDATE licenses SET status = 'active' WHERE user_id = ?", ("DISABLED_USER",))
    conn.commit()
    write_result("✅ 已启用 DISABLED_USER 的卡密")
    
    # 重新验证
    enabled_status = check_license_status("DISABLED_USER", "DISABLED-TEST-4444-5555-6666")
    write_result(f"启用后状态: {enabled_status}")
    
    enabled_valid, enabled_msg = validate_license_with_status("DISABLED_USER", "DISABLED-TEST-4444-5555-6666")
    write_result(f"启用后验证: {'✅ 通过' if enabled_valid else '❌ 失败'} - {enabled_msg}")
    
    conn.close()
    
    write_result("\n🎉 禁用验证功能测试完成！")
    write_result("✅ 状态检查功能正常")
    write_result("✅ 禁用卡密被正确拒绝")
    write_result("✅ 启用卡密可以正常使用")
    
    # 恢复原数据库
    if os.path.exists("license_data_backup.db"):
        import shutil
        shutil.copy2("license_data_backup.db", db_path)
        os.remove("license_data_backup.db")
        write_result("✅ 已恢复原数据库")
    
except Exception as e:
    write_result(f"❌ 测试失败: {e}")
    import traceback
    write_result(traceback.format_exc())

write_result("测试完成")
