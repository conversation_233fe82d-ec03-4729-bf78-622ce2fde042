@echo off
chcp 65001 >nul
title 卡密生成器

echo.
echo 🔑 拼多多采集工具 - 卡密生成器
echo ================================================

cd /d "%~dp0"

echo 📍 当前目录: %CD%
echo.

REM 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python
    echo 请确保已安装Python并添加到PATH环境变量
    echo.
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

REM 启动卡密生成器
echo 🚀 启动卡密生成器...
echo.

python start_generator.py

if errorlevel 1 (
    echo.
    echo ❌ 程序异常退出
    pause
) else (
    echo.
    echo ✅ 程序正常退出
)

exit /b 0
