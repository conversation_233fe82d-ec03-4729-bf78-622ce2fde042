"""
拼多多采集工具 - 主界面
"""
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import json
import os
from datetime import datetime

class MainWindow:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("拼多多商品数据采集工具 v1.0")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)
        
        # 设置图标（如果有的话）
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
            
        # 初始化变量
        self.spider = None
        self.is_logged_in = False
        self.is_collecting = False

        # 创建界面
        self.create_widgets()
        self.create_menu()
        self.setup_shortcuts()
        self.load_config()

        # 初始化筛选状态显示
        self.update_filter_status()
        
    def create_widgets(self):
        """创建主界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建笔记本控件（标签页）
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建各个标签页
        self.create_login_tab()
        self.create_collect_tab()
        self.create_data_tab()
        self.create_settings_tab()
        
        # 创建状态栏
        self.create_status_bar()

    def setup_shortcuts(self):
        """设置快捷键"""
        # F5 或 Ctrl+R: 刷新统计
        self.root.bind('<F5>', lambda e: self.refresh_stats())
        self.root.bind('<Control-r>', lambda e: self.refresh_stats())

        # Ctrl+D: 刷新数据
        self.root.bind('<Control-d>', lambda e: self.refresh_data())

        # Ctrl+E: 导出数据
        self.root.bind('<Control-e>', lambda e: self.export_data())

        # Ctrl+L: 登录
        self.root.bind('<Control-l>', lambda e: self.start_login())

        # Ctrl+S: 开始采集
        self.root.bind('<Control-s>', lambda e: self.start_collect())

    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="导入配置", command=self.import_config)
        file_menu.add_command(label="导出配置", command=self.export_config)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit)
        
        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="清理缓存", command=self.clear_cache)
        tools_menu.add_command(label="数据统计", command=self.show_statistics)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self.show_help)
        help_menu.add_command(label="快捷键说明", command=self.show_shortcuts)
        help_menu.add_separator()
        help_menu.add_command(label="关于", command=self.show_about)
        
    def create_login_tab(self):
        """创建登录标签页"""
        login_frame = ttk.Frame(self.notebook)
        self.notebook.add(login_frame, text="账号登录")
        
        # 登录状态显示
        status_frame = ttk.LabelFrame(login_frame, text="登录状态", padding=10)
        status_frame.pack(fill=tk.X, padx=10, pady=5)

        self.login_status_var = tk.StringVar(value="未登录")
        ttk.Label(status_frame, textvariable=self.login_status_var, font=("Arial", 12)).pack()

        # 域名显示
        self.domain_status_var = tk.StringVar(value="当前域名: mobile.pinduoduo.com")
        ttk.Label(status_frame, textvariable=self.domain_status_var,
                 font=("Arial", 9), foreground="gray").pack()
        
        # 登录控制
        control_frame = ttk.LabelFrame(login_frame, text="登录控制", padding=10)
        control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(control_frame, text="开始登录", command=self.start_login, 
                  style="Accent.TButton").pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="检查登录状态", command=self.check_login_status).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="清除登录信息", command=self.clear_login).pack(side=tk.LEFT, padx=5)
        
        # 登录说明
        info_frame = ttk.LabelFrame(login_frame, text="使用说明", padding=10)
        info_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        info_text = """
登录说明：
1. 点击"开始登录"按钮，程序会打开浏览器窗口 不要使用代理
2. 在弹出的登录对话框中输入手机号码
3. 点击获取验证码，输入收到的验证码
4. 登录成功后，程序会自动保存登录信息
5. 下次使用时可以直接进行采集，无需重复登录

注意事项：
- 请使用真实有效的手机号码
- 验证码有时效性，请及时输入
- 登录信息会保存在本地，请妥善保管
- 如遇到滑块验证，请手动完成验证
        """
        
        text_widget = tk.Text(info_frame, wrap=tk.WORD, height=15)
        text_widget.pack(fill=tk.BOTH, expand=True)
        text_widget.insert(tk.END, info_text)
        text_widget.config(state=tk.DISABLED)
        
    def create_collect_tab(self):
        """创建采集标签页"""
        collect_frame = ttk.Frame(self.notebook)
        self.notebook.add(collect_frame, text="数据采集")
        
        # 采集模式选择
        mode_frame = ttk.LabelFrame(collect_frame, text="采集模式", padding=10)
        mode_frame.pack(fill=tk.X, padx=10, pady=5)

        self.collect_mode = tk.StringVar(value="batch")
        ttk.Radiobutton(mode_frame, text="批量采集", variable=self.collect_mode,
                       value="batch", command=self.on_mode_change).pack(side=tk.LEFT, padx=10)
        ttk.Radiobutton(mode_frame, text="单品采集", variable=self.collect_mode,
                       value="single", command=self.on_mode_change).pack(side=tk.LEFT, padx=10)
        ttk.Radiobutton(mode_frame, text="批量链接", variable=self.collect_mode,
                       value="batch_urls", command=self.on_mode_change).pack(side=tk.LEFT, padx=10)
        
        # 批量采集设置
        self.batch_frame = ttk.LabelFrame(collect_frame, text="批量采集设置", padding=10)
        self.batch_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(self.batch_frame, text="采集页数:").pack(side=tk.LEFT)
        self.pages_var = tk.StringVar(value="5")
        ttk.Entry(self.batch_frame, textvariable=self.pages_var, width=10).pack(side=tk.LEFT, padx=5)
        
        # 单品采集设置
        self.single_frame = ttk.LabelFrame(collect_frame, text="单品采集设置", padding=10)

        ttk.Label(self.single_frame, text="商品链接:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.detail_url = tk.StringVar()
        ttk.Entry(self.single_frame, textvariable=self.detail_url, width=30).grid(row=0, column=1, padx=5, pady=2)

        # 批量链接设置
        self.batch_urls_frame = ttk.LabelFrame(collect_frame, text="批量链接设置", padding=10)

        # 文件选择
        file_frame = ttk.Frame(self.batch_urls_frame)
        file_frame.pack(fill=tk.X, pady=5)

        ttk.Label(file_frame, text="链接文件:").pack(side=tk.LEFT)
        self.urls_file_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.urls_file_var, width=50).pack(side=tk.LEFT, padx=5)
        ttk.Button(file_frame, text="选择文件", command=self.select_urls_file).pack(side=tk.LEFT, padx=5)

        # 链接预览
        preview_frame = ttk.Frame(self.batch_urls_frame)
        preview_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        ttk.Label(preview_frame, text="链接预览:").pack(anchor=tk.W)
        self.urls_preview = tk.Text(preview_frame, height=8, wrap=tk.WORD)
        urls_scrollbar = ttk.Scrollbar(preview_frame, orient=tk.VERTICAL, command=self.urls_preview.yview)
        self.urls_preview.configure(yscrollcommand=urls_scrollbar.set)

        self.urls_preview.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        urls_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        
        # 筛选状态显示
        filter_status_frame = ttk.LabelFrame(collect_frame, text="筛选状态", padding=10)
        filter_status_frame.pack(fill=tk.X, padx=10, pady=5)

        self.filter_status_var = tk.StringVar(value="正在加载筛选状态...")
        filter_label = ttk.Label(filter_status_frame, textvariable=self.filter_status_var,
                                font=("Arial", 9), foreground="blue")
        filter_label.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 配置状态提示
        self.config_status_var = tk.StringVar(value="")
        config_status_label = ttk.Label(filter_status_frame, textvariable=self.config_status_var,
                                       font=("Arial", 8), foreground="orange")
        config_status_label.pack(side=tk.RIGHT, padx=5)

        ttk.Button(filter_status_frame, text="刷新状态",
                  command=self.update_filter_status).pack(side=tk.RIGHT)

        # 采集控制
        control_frame = ttk.LabelFrame(collect_frame, text="采集控制", padding=10)
        control_frame.pack(fill=tk.X, padx=10, pady=5)

        self.start_btn = ttk.Button(control_frame, text="开始采集", command=self.start_collect,
                                   style="Accent.TButton")
        self.start_btn.pack(side=tk.LEFT, padx=5)

        self.stop_btn = ttk.Button(control_frame, text="停止采集", command=self.stop_collect,
                                  state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=5)

        # 浏览器控制按钮
        self.browser_visible = False
        self.browser_btn = ttk.Button(control_frame, text="显示浏览器", command=self.toggle_browser)
        self.browser_btn.pack(side=tk.LEFT, padx=5)

        # 手动验证按钮
        self.manual_verify_btn = ttk.Button(control_frame, text="手动验证", command=self.manual_verify)
        self.manual_verify_btn.pack(side=tk.LEFT, padx=5)
        
        # 进度显示
        progress_frame = ttk.LabelFrame(collect_frame, text="采集进度", padding=10)
        progress_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.progress_var = tk.StringVar(value="等待开始...")
        ttk.Label(progress_frame, textvariable=self.progress_var).pack()
        
        self.progress_bar = ttk.Progressbar(progress_frame, mode='indeterminate')
        self.progress_bar.pack(fill=tk.X, pady=5)
        
        # 日志显示
        log_frame = ttk.LabelFrame(collect_frame, text="采集日志", padding=10)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 创建日志文本框和滚动条
        log_text_frame = tk.Frame(log_frame)
        log_text_frame.pack(fill=tk.BOTH, expand=True)
        
        self.log_text = tk.Text(log_text_frame, wrap=tk.WORD, height=10)
        log_scrollbar = ttk.Scrollbar(log_text_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 日志控制按钮
        log_control_frame = tk.Frame(log_frame)
        log_control_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(log_control_frame, text="清空日志", command=self.clear_log).pack(side=tk.LEFT, padx=5)
        ttk.Button(log_control_frame, text="保存日志", command=self.save_log).pack(side=tk.LEFT, padx=5)
        
    def create_data_tab(self):
        """创建数据管理标签页"""
        data_frame = ttk.Frame(self.notebook)
        self.notebook.add(data_frame, text="数据管理")
        
        # 数据统计
        stats_frame = ttk.LabelFrame(data_frame, text="数据统计", padding=10)
        stats_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.stats_var = tk.StringVar(value="总计: 0 个商品 | 正在加载...")
        stats_label = ttk.Label(stats_frame, textvariable=self.stats_var, font=("Arial", 12))
        stats_label.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 刷新按钮
        refresh_btn = ttk.Button(stats_frame, text="🔄 刷新", command=self.refresh_stats)
        refresh_btn.pack(side=tk.RIGHT, padx=(10, 0))

        # 详细统计按钮
        detail_btn = ttk.Button(stats_frame, text="📊 详细", command=self.show_statistics)
        detail_btn.pack(side=tk.RIGHT)
        
        # 数据导出
        export_frame = ttk.LabelFrame(data_frame, text="数据导出", padding=10)
        export_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(export_frame, text="导出格式:").pack(side=tk.LEFT)
        self.export_format = tk.StringVar(value="csv")
        ttk.Radiobutton(export_frame, text="CSV", variable=self.export_format, value="csv").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(export_frame, text="Excel", variable=self.export_format, value="excel").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(export_frame, text="JSON", variable=self.export_format, value="json").pack(side=tk.LEFT, padx=5)
        
        ttk.Button(export_frame, text="导出数据", command=self.export_data).pack(side=tk.RIGHT, padx=5)
        ttk.Button(export_frame, text="选择导出路径", command=self.select_export_path).pack(side=tk.RIGHT, padx=5)
        
        # 数据预览
        preview_frame = ttk.LabelFrame(data_frame, text="数据预览", padding=10)
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 创建表格
        columns = ("ID", "商品名称", "价格", "采集时间")
        self.data_tree = ttk.Treeview(preview_frame, columns=columns, show="headings", height=15)
        
        for col in columns:
            self.data_tree.heading(col, text=col)
            self.data_tree.column(col, width=150)
        
        # 添加滚动条
        tree_scrollbar = ttk.Scrollbar(preview_frame, orient=tk.VERTICAL, command=self.data_tree.yview)
        self.data_tree.configure(yscrollcommand=tree_scrollbar.set)
        
        self.data_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tree_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 绑定双击事件查看详情
        self.data_tree.bind("<Double-1>", lambda e: self.view_details())

        # 创建右键菜单
        self.create_data_context_menu()
        
        # 数据操作按钮
        data_control_frame = tk.Frame(data_frame)
        data_control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(data_control_frame, text="删除选中", command=self.delete_selected).pack(side=tk.LEFT, padx=5)
        ttk.Button(data_control_frame, text="查看详情", command=self.view_details).pack(side=tk.LEFT, padx=5)

    def create_settings_tab(self):
        """创建设置标签页"""
        settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(settings_frame, text="系统设置")

        # 基本设置
        basic_frame = ttk.LabelFrame(settings_frame, text="基本设置", padding=10)
        basic_frame.pack(fill=tk.X, padx=10, pady=5)

        # 无头模式
        self.headless_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(basic_frame, text="无头模式运行（推荐）", variable=self.headless_var).pack(anchor=tk.W)

        # 延时设置
        delay_frame = tk.Frame(basic_frame)
        delay_frame.pack(fill=tk.X, pady=5)

        ttk.Label(delay_frame, text="采集延时（秒）:").pack(side=tk.LEFT)
        self.delay_min_var = tk.StringVar(value="3")
        ttk.Entry(delay_frame, textvariable=self.delay_min_var, width=5).pack(side=tk.LEFT, padx=5)
        ttk.Label(delay_frame, text="到").pack(side=tk.LEFT)
        self.delay_max_var = tk.StringVar(value="5")
        ttk.Entry(delay_frame, textvariable=self.delay_max_var, width=5).pack(side=tk.LEFT, padx=5)

        # 重试次数
        retry_frame = tk.Frame(basic_frame)
        retry_frame.pack(fill=tk.X, pady=5)

        ttk.Label(retry_frame, text="重试次数:").pack(side=tk.LEFT)
        self.retry_var = tk.StringVar(value="3")
        ttk.Entry(retry_frame, textvariable=self.retry_var, width=5).pack(side=tk.LEFT, padx=5)

        # 路径设置
        path_frame = ttk.LabelFrame(settings_frame, text="路径设置", padding=10)
        path_frame.pack(fill=tk.X, padx=10, pady=5)

        # 结果保存路径
        result_path_frame = tk.Frame(path_frame)
        result_path_frame.pack(fill=tk.X, pady=2)

        ttk.Label(result_path_frame, text="结果保存路径:").pack(side=tk.LEFT)
        self.result_path_var = tk.StringVar(value="./result/")
        ttk.Entry(result_path_frame, textvariable=self.result_path_var, width=40).pack(side=tk.LEFT, padx=5)
        ttk.Button(result_path_frame, text="浏览", command=lambda: self.browse_folder(self.result_path_var)).pack(side=tk.LEFT)

        # 导出路径
        export_path_frame = tk.Frame(path_frame)
        export_path_frame.pack(fill=tk.X, pady=2)

        ttk.Label(export_path_frame, text="导出保存路径:").pack(side=tk.LEFT)
        self.export_path_var = tk.StringVar(value="./export/")
        ttk.Entry(export_path_frame, textvariable=self.export_path_var, width=40).pack(side=tk.LEFT, padx=5)
        ttk.Button(export_path_frame, text="浏览", command=lambda: self.browse_folder(self.export_path_var)).pack(side=tk.LEFT)

        # 价格筛选设置
        price_filter_frame = ttk.LabelFrame(settings_frame, text="价格筛选", padding=10)
        price_filter_frame.pack(fill=tk.X, padx=10, pady=5)

        # 启用价格筛选
        self.price_filter_enabled_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(price_filter_frame, text="启用价格筛选",
                       variable=self.price_filter_enabled_var,
                       command=self.on_price_filter_toggle).pack(anchor=tk.W, pady=2)

        # 筛选模式
        mode_frame = tk.Frame(price_filter_frame)
        mode_frame.pack(fill=tk.X, pady=5)

        ttk.Label(mode_frame, text="筛选模式:").pack(side=tk.LEFT)
        self.price_filter_mode = tk.StringVar(value="range")

        mode_combo = ttk.Combobox(mode_frame, textvariable=self.price_filter_mode,
                                 values=["range", "less_than", "greater_than"],
                                 state="readonly", width=12)
        mode_combo.pack(side=tk.LEFT, padx=5)
        mode_combo.bind('<<ComboboxSelected>>', self.on_price_mode_change)

        # 价格范围设置
        self.price_range_frame = tk.Frame(price_filter_frame)
        self.price_range_frame.pack(fill=tk.X, pady=5)

        ttk.Label(self.price_range_frame, text="最低价格:").pack(side=tk.LEFT)
        self.min_price_var = tk.StringVar(value="0.0")
        min_price_entry = ttk.Entry(self.price_range_frame, textvariable=self.min_price_var, width=8)
        min_price_entry.pack(side=tk.LEFT, padx=5)
        min_price_entry.bind('<KeyRelease>', lambda e: (self.update_filter_status(), self.mark_config_changed()))

        ttk.Label(self.price_range_frame, text="最高价格:").pack(side=tk.LEFT, padx=(10, 0))
        self.max_price_var = tk.StringVar(value="0.5")
        max_price_entry = ttk.Entry(self.price_range_frame, textvariable=self.max_price_var, width=8)
        max_price_entry.pack(side=tk.LEFT, padx=5)
        max_price_entry.bind('<KeyRelease>', lambda e: (self.update_filter_status(), self.mark_config_changed()))

        # 快速设置按钮
        quick_frame = tk.Frame(price_filter_frame)
        quick_frame.pack(fill=tk.X, pady=5)

        ttk.Label(quick_frame, text="快速设置:").pack(side=tk.LEFT)
        ttk.Button(quick_frame, text="0-1元", command=lambda: self.set_price_range(0, 1)).pack(side=tk.LEFT, padx=2)
        ttk.Button(quick_frame, text="1-5元", command=lambda: self.set_price_range(1, 5)).pack(side=tk.LEFT, padx=2)
        ttk.Button(quick_frame, text="5-10元", command=lambda: self.set_price_range(5, 10)).pack(side=tk.LEFT, padx=2)
        ttk.Button(quick_frame, text="10-50元", command=lambda: self.set_price_range(10, 50)).pack(side=tk.LEFT, padx=2)

        # 虚拟商品筛选设置
        virtual_filter_frame = ttk.LabelFrame(settings_frame, text="虚拟商品筛选", padding=10)
        virtual_filter_frame.pack(fill=tk.X, padx=10, pady=5)

        # 启用虚拟商品筛选
        self.virtual_filter_enabled_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(virtual_filter_frame, text="启用虚拟商品筛选",
                       variable=self.virtual_filter_enabled_var,
                       command=lambda: (self.update_filter_status(), self.mark_config_changed())).pack(anchor=tk.W, pady=2)

        # 虚拟商品筛选模式
        virtual_mode_frame = tk.Frame(virtual_filter_frame)
        virtual_mode_frame.pack(fill=tk.X, pady=5)

        ttk.Label(virtual_mode_frame, text="筛选模式:").pack(side=tk.LEFT)
        self.virtual_filter_mode = tk.StringVar(value="exclude")

        virtual_combo = ttk.Combobox(virtual_mode_frame, textvariable=self.virtual_filter_mode,
                                   values=["include", "exclude", "only"],
                                   state="readonly", width=12)
        virtual_combo.pack(side=tk.LEFT, padx=5)
        virtual_combo.bind('<<ComboboxSelected>>', lambda e: (self.update_filter_status(), self.mark_config_changed()))

        # 模式说明
        mode_desc = tk.Label(virtual_filter_frame, text="include: 包含所有商品  exclude: 排除虚拟商品  only: 仅虚拟商品",
                           font=("Arial", 8), fg="gray")
        mode_desc.pack(anchor=tk.W, pady=2)

        # 设置控制
        settings_control_frame = ttk.LabelFrame(settings_frame, text="设置控制", padding=10)
        settings_control_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(settings_control_frame, text="保存设置", command=self.save_settings).pack(side=tk.LEFT, padx=5)
        ttk.Button(settings_control_frame, text="应用配置", command=self.apply_config,
                  style="Accent.TButton").pack(side=tk.LEFT, padx=5)
        ttk.Button(settings_control_frame, text="重置设置", command=self.reset_settings).pack(side=tk.LEFT, padx=5)
        ttk.Button(settings_control_frame, text="导入设置", command=self.import_settings).pack(side=tk.LEFT, padx=5)
        ttk.Button(settings_control_frame, text="导出设置", command=self.export_settings).pack(side=tk.LEFT, padx=5)

    def create_status_bar(self):
        """创建状态栏"""
        self.status_frame = ttk.Frame(self.root)
        self.status_frame.pack(side=tk.BOTTOM, fill=tk.X)

        # 状态标签
        self.status_var = tk.StringVar(value="就绪")
        ttk.Label(self.status_frame, textvariable=self.status_var).pack(side=tk.LEFT, padx=5)

        # 分隔符
        ttk.Separator(self.status_frame, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)

        # 时间显示
        self.time_var = tk.StringVar()
        ttk.Label(self.status_frame, textvariable=self.time_var).pack(side=tk.RIGHT, padx=5)

        # 更新时间
        self.update_time()

    def update_time(self):
        """更新时间显示"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_var.set(current_time)
        self.root.after(1000, self.update_time)

    def on_mode_change(self):
        """采集模式改变时的处理"""
        mode = self.collect_mode.get()

        # 隐藏所有框架
        self.batch_frame.pack_forget()
        self.single_frame.pack_forget()
        self.batch_urls_frame.pack_forget()

        # 显示对应的框架
        if mode == "batch":
            self.batch_frame.pack(fill=tk.X, padx=10, pady=5)
        elif mode == "single":
            self.single_frame.pack(fill=tk.X, padx=10, pady=5)
        elif mode == "batch_urls":
            self.batch_urls_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

    def select_urls_file(self):
        """选择链接文件"""
        from tkinter import filedialog

        file_path = filedialog.askopenfilename(
            title="选择商品链接文件",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )

        if file_path:
            self.urls_file_var.set(file_path)
            self.load_urls_preview(file_path)


    def load_urls_preview(self, file_path):
        """加载链接预览"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            self.urls_preview.delete(1.0, tk.END)

            # 只显示前20行
            lines = content.split('\n')
            preview_lines = lines[:20]

            if len(lines) > 20:
                preview_lines.append(f"... 还有 {len(lines) - 20} 行")

            self.urls_preview.insert(1.0, '\n'.join(preview_lines))

        except Exception as e:
            pass


    def on_price_filter_toggle(self):
        """价格筛选开关切换"""
        enabled = self.price_filter_enabled_var.get()
        self.log_message(f"价格筛选已{'启用' if enabled else '禁用'}", "INFO")
        self.update_filter_status()
        self.mark_config_changed()

    def on_price_mode_change(self, event=None):
        """价格筛选模式改变"""
        mode = self.price_filter_mode.get()
        # 根据模式调整界面显示
        if mode == "range":
            # 显示最低和最高价格
            for widget in self.price_range_frame.winfo_children():
                widget.pack_configure()
        elif mode == "less_than":
            # 只显示最高价格相关控件
            pass
        elif mode == "greater_than":
            # 只显示最低价格相关控件
            pass
        self.update_filter_status()
        self.mark_config_changed()

    def mark_config_changed(self):
        """标记配置已更改"""
        self.config_status_var.set("⚠ 配置已更改，请在设置页面点击'应用配置'")

    def set_price_range(self, min_price, max_price):
        """设置价格范围"""
        self.min_price_var.set(str(min_price))
        self.max_price_var.set(str(max_price))
        self.price_filter_mode.set("range")
        self.log_message(f"价格范围已设置为: ¥{min_price} - ¥{max_price}", "INFO")
        self.update_filter_status()
        self.mark_config_changed()

    def update_filter_status(self):
        """更新筛选状态显示"""
        try:
            status_parts = []

            # 价格筛选状态
            if self.price_filter_enabled_var.get():
                mode = self.price_filter_mode.get()
                min_price = float(self.min_price_var.get())
                max_price = float(self.max_price_var.get())

                if mode == "range":
                    status_parts.append(f"价格: ¥{min_price}-¥{max_price}")
                elif mode == "less_than":
                    status_parts.append(f"价格: <¥{max_price}")
                elif mode == "greater_than":
                    status_parts.append(f"价格: >¥{min_price}")
            else:
                status_parts.append("价格: 无限制")

            # 虚拟商品筛选状态
            if self.virtual_filter_enabled_var.get():
                virtual_mode = self.virtual_filter_mode.get()
                if virtual_mode == "exclude":
                    status_parts.append("虚拟商品: 排除")
                elif virtual_mode == "only":
                    status_parts.append("虚拟商品: 仅虚拟")
                elif virtual_mode == "include":
                    status_parts.append("虚拟商品: 包含所有")
            else:
                status_parts.append("虚拟商品: 无限制")

            status_text = " | ".join(status_parts)
            self.filter_status_var.set(status_text)

        except ValueError:
            self.filter_status_var.set("筛选配置有误，请检查价格设置")

        except Exception as e:
            self.urls_preview.delete(1.0, tk.END)
            self.urls_preview.insert(1.0, f"预览失败: {str(e)}")

    def log_message(self, message, level="INFO"):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}\n"

        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)

        # 根据日志级别设置颜色
        if level == "ERROR":
            self.log_text.tag_add("error", f"end-{len(log_entry)}c", "end-1c")
            self.log_text.tag_config("error", foreground="red")
        elif level == "WARNING":
            self.log_text.tag_add("warning", f"end-{len(log_entry)}c", "end-1c")
            self.log_text.tag_config("warning", foreground="orange")
        elif level == "SUCCESS":
            self.log_text.tag_add("success", f"end-{len(log_entry)}c", "end-1c")
            self.log_text.tag_config("success", foreground="green")

    def update_status(self, message):
        """更新状态栏"""
        self.status_var.set(message)

    def load_config(self):
        """加载配置"""
        try:
            if os.path.exists("config.json"):
                with open("config.json", "r", encoding="utf-8") as f:
                    config = json.load(f)

                # 更新界面设置
                self.headless_var.set(config.get("headless", True))
                self.delay_min_var.set(str(config.get("delay_min", 3)))
                self.delay_max_var.set(str(config.get("delay_max", 5)))
                self.retry_var.set(str(config.get("retry_times", 3)))
                self.result_path_var.set(config.get("result_path", "./result/"))
                self.export_path_var.set(config.get("export_path", "./export/"))

                # 加载价格筛选配置
                price_filter = config.get("price_filter", {})
                self.price_filter_enabled_var.set(price_filter.get("enabled", True))
                self.price_filter_mode.set(price_filter.get("mode", "range"))
                self.min_price_var.set(str(price_filter.get("min_price", 0.0)))
                self.max_price_var.set(str(price_filter.get("max_price", 0.5)))

                # 加载虚拟商品筛选配置
                virtual_filter = config.get("virtual_goods_filter", {})
                self.virtual_filter_enabled_var.set(virtual_filter.get("enabled", True))
                self.virtual_filter_mode.set(virtual_filter.get("mode", "exclude"))

                self.log_message("配置加载成功", "SUCCESS")
        except Exception as e:
            self.log_message(f"配置加载失败: {str(e)}", "ERROR")

    def save_settings(self):
        """保存设置"""
        try:
            config = {
                "headless": self.headless_var.get(),
                "delay_min": int(self.delay_min_var.get()),
                "delay_max": int(self.delay_max_var.get()),
                "retry_times": int(self.retry_var.get()),
                "result_path": self.result_path_var.get(),
                "export_path": self.export_path_var.get(),
                "cookies_path": "./cookies.json",
                "url_path": "./url.txt",
                "cache_path": "./cache/ids.json",
                # 价格筛选配置
                "price_filter": {
                    "enabled": self.price_filter_enabled_var.get(),
                    "mode": self.price_filter_mode.get(),
                    "min_price": float(self.min_price_var.get()),
                    "max_price": float(self.max_price_var.get())
                },
                # 虚拟商品筛选配置
                "virtual_goods_filter": {
                    "enabled": self.virtual_filter_enabled_var.get(),
                    "mode": self.virtual_filter_mode.get(),
                    "keywords": [
                        "充值", "会员", "虚拟", "话费", "流量", "Q币", "点券", "代充",
                        "游戏币", "钻石", "金币", "积分", "卡密", "激活码", "兑换码",
                        "网费", "电费", "水费", "燃气费", "缴费", "代缴", "充值卡",
                        "游戏充值", "手游", "端游", "网游", "在线充值", "自动发货"
                    ]
                }
            }

            with open("config.json", "w", encoding="utf-8") as f:
                json.dump(config, f, indent=2, ensure_ascii=False)

            self.log_message("设置保存成功", "SUCCESS")
            messagebox.showinfo("成功", "设置已保存")
        except Exception as e:
            self.log_message(f"设置保存失败: {str(e)}", "ERROR")
            messagebox.showerror("错误", f"设置保存失败: {str(e)}")

    def apply_config(self):
        """应用配置到爬虫"""
        try:
            # 调用主程序的更新配置方法
            if hasattr(self, '_update_spider_config_handler'):
                self._update_spider_config_handler()
                self.config_status_var.set("✓ 配置已应用")
                self.log_message("配置已应用到爬虫", "SUCCESS")
                messagebox.showinfo("成功", "配置已应用到爬虫")
            else:
                self.log_message("爬虫未初始化，无法应用配置", "WARNING")
                messagebox.showwarning("警告", "爬虫未初始化，无法应用配置")
        except Exception as e:
            self.log_message(f"应用配置失败: {str(e)}", "ERROR")
            messagebox.showerror("错误", f"应用配置失败: {str(e)}")

    def browse_folder(self, var):
        """浏览文件夹"""
        folder = filedialog.askdirectory()
        if folder:
            var.set(folder)

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)

    def save_log(self):
        """保存日志"""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
            )
            if filename:
                with open(filename, "w", encoding="utf-8") as f:
                    f.write(self.log_text.get(1.0, tk.END))
                self.log_message(f"日志已保存到: {filename}", "SUCCESS")
        except Exception as e:
            self.log_message(f"日志保存失败: {str(e)}", "ERROR")

    # 事件处理方法 - 这些方法会被主程序重新绑定
    def start_login(self):
        """开始登录"""
        if hasattr(self, '_login_handler'):
            self._login_handler()
        else:
            self.log_message("登录功能未初始化，请重启程序", "ERROR")

    def check_login_status(self):
        """检查登录状态"""
        if hasattr(self, '_check_login_handler'):
            self._check_login_handler()
        else:
            self.log_message("登录状态检查功能未初始化", "ERROR")

    def clear_login(self):
        """清除登录信息"""
        if hasattr(self, '_clear_login_handler'):
            self._clear_login_handler()
        else:
            self.log_message("清除登录功能未初始化", "ERROR")

    def start_collect(self):
        """开始采集"""
        if hasattr(self, '_start_collect_handler'):
            self._start_collect_handler()
        else:
            self.log_message("采集功能未初始化，请重启程序", "ERROR")

    def stop_collect(self):
        """停止采集"""
        if hasattr(self, '_stop_collect_handler'):
            self._stop_collect_handler()
        else:
            self.log_message("停止采集功能未初始化", "ERROR")

    def refresh_stats(self):
        """刷新统计"""
        if hasattr(self, '_refresh_stats_handler') and callable(self._refresh_stats_handler):
            # 如果有外部处理器，调用外部处理器
            self._refresh_stats_handler()
        else:
            self.log_message("正在刷新统计信息...", "INFO")

    def export_data(self):
        """导出数据"""
        self.log_message("导出数据功能将在后续实现", "INFO")

    def select_export_path(self):
        """选择导出路径"""
        folder = filedialog.askdirectory()
        if folder:
            self.export_path_var.set(folder)

    def delete_selected(self):
        """删除选中数据"""
        try:
            selected_items = self.data_tree.selection()
            if not selected_items:
                messagebox.showwarning("警告", "请先选择要删除的数据")
                return

            # 确认删除
            count = len(selected_items)
            if not messagebox.askyesno("确认删除",
                                     f"确定要删除选中的 {count} 条数据吗？\n\n此操作不可恢复！"):
                return

            # 调用主程序的删除处理器
            if hasattr(self, 'delete_selected_handler'):
                self.delete_selected_handler()
            else:
                self.log_message("删除功能未初始化", "ERROR")

        except Exception as e:
            self.log_message(f"删除数据失败: {str(e)}", "ERROR")
            messagebox.showerror("错误", f"删除数据失败: {str(e)}")

    def view_details(self):
        """查看详情"""
        try:
            selected_items = self.data_tree.selection()
            if not selected_items:
                messagebox.showwarning("警告", "请先选择要查看的数据")
                return

            # 调用主程序的查看详情处理器
            if hasattr(self, 'view_details_handler'):
                self.view_details_handler()
            else:
                self.log_message("查看详情功能未初始化", "ERROR")

        except Exception as e:
            self.log_message(f"查看详情失败: {str(e)}", "ERROR")
            messagebox.showerror("错误", f"查看详情失败: {str(e)}")

    def refresh_data(self):
        """刷新数据预览"""
        try:
            # 调用主程序的刷新数据处理器
            if hasattr(self, 'refresh_data_handler'):
                self.refresh_data_handler()
                self.log_message("数据预览已刷新", "SUCCESS")
            else:
                self.log_message("刷新数据功能未初始化", "ERROR")

        except Exception as e:
            self.log_message(f"刷新数据失败: {str(e)}", "ERROR")
            messagebox.showerror("错误", f"刷新数据失败: {str(e)}")

    def create_data_context_menu(self):
        """创建数据表格的右键菜单"""
        self.data_context_menu = tk.Menu(self.root, tearoff=0)
        self.data_context_menu.add_command(label="📋 查看详情", command=self.view_details)
        self.data_context_menu.add_command(label="📋 复制商品ID", command=self.copy_selected_id)
        self.data_context_menu.add_separator()
        self.data_context_menu.add_command(label="🔄 刷新数据", command=self.refresh_data)
        self.data_context_menu.add_separator()
        self.data_context_menu.add_command(label="🗑️ 删除选中", command=self.delete_selected)

        # 绑定右键菜单
        self.data_tree.bind("<Button-3>", self.show_data_context_menu)

    def show_data_context_menu(self, event):
        """显示数据表格右键菜单"""
        try:
            # 选中右键点击的项
            item = self.data_tree.identify_row(event.y)
            if item:
                self.data_tree.selection_set(item)
                self.data_context_menu.post(event.x_root, event.y_root)
        except Exception as e:
            self.log_message(f"显示右键菜单失败: {str(e)}", "ERROR")

    def copy_selected_id(self):
        """复制选中商品的ID"""
        try:
            selected_items = self.data_tree.selection()
            if not selected_items:
                messagebox.showwarning("警告", "请先选择要复制ID的数据")
                return

            # 获取第一个选中项的ID
            item = selected_items[0]
            values = self.data_tree.item(item, "values")
            product_id = values[0]

            # 复制到剪贴板
            self.root.clipboard_clear()
            self.root.clipboard_append(product_id)

            self.log_message(f"商品ID已复制: {product_id}", "SUCCESS")
            messagebox.showinfo("复制成功", f"商品ID已复制到剪贴板:\n{product_id}")

        except Exception as e:
            self.log_message(f"复制商品ID失败: {str(e)}", "ERROR")
            messagebox.showerror("错误", f"复制商品ID失败: {str(e)}")

    def toggle_browser(self):
        """切换浏览器显示/隐藏"""
        if hasattr(self, '_toggle_browser_handler') and callable(self._toggle_browser_handler):
            self._toggle_browser_handler()
        else:
            self.log_message("浏览器控制功能未初始化", "WARNING")

    def manual_verify(self):
        """手动验证（过滑块等）"""
        if hasattr(self, '_manual_verify_handler') and callable(self._manual_verify_handler):
            self._manual_verify_handler()
        else:
            self.log_message("手动验证功能未初始化", "WARNING")

    def update_browser_button(self, visible):
        """更新浏览器按钮状态"""
        self.browser_visible = visible
        if visible:
            self.browser_btn.config(text="隐藏浏览器")
        else:
            self.browser_btn.config(text="显示浏览器")

    def reset_settings(self):
        """重置设置"""
        self.headless_var.set(True)
        self.delay_min_var.set("3")
        self.delay_max_var.set("5")
        self.retry_var.set("3")
        self.result_path_var.set("./result/")
        self.export_path_var.set("./export/")
        self.log_message("设置已重置", "INFO")

    def import_settings(self):
        """导入设置"""
        self.log_message("导入设置功能将在后续实现", "INFO")

    def export_settings(self):
        """导出设置"""
        self.log_message("导出设置功能将在后续实现", "INFO")

    def import_config(self):
        """导入配置"""
        self.log_message("导入配置功能将在后续实现", "INFO")

    def export_config(self):
        """导出配置"""
        self.log_message("导出配置功能将在后续实现", "INFO")

    def clear_cache(self):
        """清理缓存"""
        self.log_message("清理缓存功能将在后续实现", "INFO")

    def show_statistics(self):
        """显示统计"""
        self.log_message("显示统计功能将在后续实现", "INFO")

    def show_help(self):
        """显示帮助"""
        help_text = """
拼多多商品数据采集工具使用说明

1. 账号登录
   - 首次使用需要先登录拼多多账号
   - 点击"开始登录"按钮，按提示输入手机号和验证码
   - 登录成功后会自动保存登录信息

2. 数据采集
   - 支持批量采集和单品采集两种模式
   - 批量采集：设置采集页数，自动采集多页商品
   - 单品采集：输入商品链接进行单个商品采集

3. 数据管理
   - 查看已采集的商品数据
   - 支持导出为CSV、Excel、JSON格式
   - 可以删除不需要的数据

4. 系统设置
   - 调整采集延时、重试次数等参数
   - 设置数据保存路径
   - 配置浏览器运行模式

注意事项：
- 请遵守网站使用条款，合理控制采集频率
- 采集的数据仅供个人研究使用
- 建议定期备份重要数据
        """
        messagebox.showinfo("使用说明", help_text)

    def show_about(self):
        """显示关于"""
        about_text = """
拼多多商品数据采集工具 v1.0

开发者：oranice
技术栈：Python + tkinter + DrissionPage
功能：商品数据采集、数据管理、数据导出

免责声明：
本工具仅供学习和研究使用，请遵守相关法律法规
和网站的使用条款，不得用于任何商业或非法用途。
使用本工具所产生的任何后果由使用者自行承担。
        """
        messagebox.showinfo("关于", about_text)

    def show_shortcuts(self):
        """显示快捷键说明"""
        shortcuts_text = """
⌨️ 快捷键说明

🔄 数据刷新:
   • F5 或 Ctrl+R: 刷新统计信息
   • Ctrl+D: 刷新数据列表

📊 功能操作:
   • Ctrl+L: 开始登录
   • Ctrl+S: 开始采集
   • Ctrl+E: 导出数据

💡 提示:
   • 统计信息会在采集完成后自动刷新
   • 可以随时按F5查看最新统计
   • 数据不一致时会显示⚠符号
        """
        messagebox.showinfo("快捷键说明", shortcuts_text)

    def run(self):
        """运行主界面"""
        self.root.mainloop()
