#!/usr/bin/env python3
import sys
import os
sys.path.insert(0, '.')

def test_license_system():
    """测试卡密系统"""
    print("🔧 测试修复后的卡密系统")
    print("=" * 50)
    
    try:
        # 导入模块
        import license_system.crypto_utils as crypto
        print("✅ 模块导入成功")
        
        # 测试基础加密
        print("\n🔐 测试基础加密...")
        test_data = "Hello, License System!"
        encrypted = crypto.CryptoUtils.encrypt_data(test_data)
        decrypted = crypto.CryptoUtils.decrypt_data(encrypted)
        
        print(f"原始数据: {test_data}")
        print(f"加密后: {encrypted[:50]}...")
        print(f"解密后: {decrypted}")
        print(f"一致性: {'✅' if test_data == decrypted else '❌'}")
        
        if test_data != decrypted:
            print("❌ 基础加密测试失败")
            return False
        
        # 测试卡密生成
        print("\n🎫 测试卡密生成...")
        license_key = crypto.LicenseGenerator.generate_license(
            user_id="TEST_USER_001",
            license_type="professional",
            duration_days=365,
            features=["basic", "batch_collect", "export_data"],
            hardware_fingerprint="test_hardware_fp"
        )
        
        print(f"✅ 卡密生成成功")
        print(f"🔑 卡密: {license_key}")
        print(f"📏 长度: {len(license_key)}")
        print(f"🔗 格式: {license_key.count('-')} 个连字符")
        
        # 检查格式
        segments = license_key.split('-')
        print(f"📋 段数: {len(segments)}")
        for i, seg in enumerate(segments):
            print(f"   段{i+1}: '{seg}' (长度: {len(seg)})")
        
        # 测试卡密解码
        print("\n🔓 测试卡密解码...")
        decoded_data = crypto.CryptoUtils.decode_license_data(license_key)
        
        print("✅ 卡密解码成功")
        print("📋 解码后的数据:")
        for key, value in decoded_data.items():
            print(f"   {key}: {value}")
        
        # 验证数据一致性
        print("\n🔍 验证数据一致性...")
        if decoded_data.get('user_id') == 'TEST_USER_001':
            print("✅ 用户ID一致")
        else:
            print(f"⚠️ 用户ID不一致: 期望 'TEST_USER_001', 实际 '{decoded_data.get('user_id')}'")
        
        if decoded_data.get('license_type') == 'professional':
            print("✅ 授权类型一致")
        else:
            print(f"⚠️ 授权类型不一致: 期望 'professional', 实际 '{decoded_data.get('license_type')}'")
        
        # 测试多次生成的唯一性
        print("\n🔄 测试卡密唯一性...")
        license_key2 = crypto.LicenseGenerator.generate_license(
            user_id="TEST_USER_002",
            license_type="personal",
            duration_days=30,
            features=["basic"],
            hardware_fingerprint=None
        )
        
        print(f"🎫 第二个卡密: {license_key2}")
        
        if license_key != license_key2:
            print("✅ 卡密唯一性验证通过")
        else:
            print("❌ 卡密相同，唯一性验证失败")
        
        # 测试第二个卡密的解码
        decoded_data2 = crypto.CryptoUtils.decode_license_data(license_key2)
        print(f"✅ 第二个卡密解码成功，用户ID: {decoded_data2.get('user_id')}")
        
        print("\n🎉 所有测试通过！卡密系统正常工作！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_encryption_details():
    """测试加密细节"""
    print("\n🔍 加密细节测试")
    print("=" * 50)
    
    try:
        import license_system.crypto_utils as crypto
        
        # 检查加密库可用性
        print(f"🔧 CRYPTO_AVAILABLE: {crypto.CRYPTO_AVAILABLE}")
        
        if crypto.CRYPTO_AVAILABLE:
            print("✅ 使用 AES-256 加密")
        else:
            print("⚠️ 使用 Base64 降级方案")
        
        # 测试密钥处理
        from license_system.config import ENCRYPTION_KEY
        print(f"🔑 原始密钥长度: {len(ENCRYPTION_KEY)} 字节")
        
        # 测试加密密钥生成
        import hashlib
        if len(ENCRYPTION_KEY) != 32:
            processed_key = hashlib.sha256(ENCRYPTION_KEY).digest()
            print(f"🔧 处理后密钥长度: {len(processed_key)} 字节")
        else:
            print("✅ 密钥长度正确")
        
        return True
        
    except Exception as e:
        print(f"❌ 加密细节测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 卡密系统完整测试")
    print("=" * 60)
    
    # 测试主要功能
    main_test_ok = test_license_system()
    
    # 测试加密细节
    detail_test_ok = test_encryption_details()
    
    print("\n📊 测试总结")
    print("=" * 50)
    print(f"🎫 卡密系统测试: {'✅ 通过' if main_test_ok else '❌ 失败'}")
    print(f"🔐 加密细节测试: {'✅ 通过' if detail_test_ok else '❌ 失败'}")
    
    if main_test_ok and detail_test_ok:
        print("\n🎉 所有测试通过！卡密系统已修复并正常工作！")
        print("✅ 卡密生成确实使用了加密功能")
        print("✅ 卡密格式正确：XXXX-XXXX-XXXX-XXXX-XXXX")
        print("✅ 加密解密流程完整")
    else:
        print("\n❌ 部分测试失败，请检查错误信息")
    
    print("\n" + "=" * 60)
