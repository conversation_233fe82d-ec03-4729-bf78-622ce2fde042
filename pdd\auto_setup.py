"""
自动环境配置和打包脚本
支持开发版和发布版的不同构建
"""
import os
import sys
import subprocess
import urllib.request
import zipfile
import shutil
import argparse
from pathlib import Path

def check_python():
    """检查Python环境"""
    print("🐍 Checking Python...")
    try:
        version = sys.version_info
        if version.major >= 3 and version.minor >= 7:
            print(f"✅ Python {version.major}.{version.minor}.{version.micro}")
            return True
        else:
            print(f"❌ Python {version.major}.{version.minor} is too old (need 3.7+)")
            return False
    except:
        print("❌ Python check failed")
        return False

def ensure_pip():
    """确保pip可用"""
    print("📦 Checking pip...")
    try:
        import pip
        print("✅ pip available")
        return True
    except ImportError:
        print("📦 Installing pip...")
        try:
            subprocess.run([sys.executable, "-m", "ensurepip", "--upgrade"], check=True)
            print("✅ pip installed")
            return True
        except:
            print("❌ Failed to install pip")
            return False

def install_package(package):
    """安装单个包"""
    print(f"📦 Installing {package}...")
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", package, "--user"
        ], check=True, capture_output=True)
        print(f"✅ {package} installed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install {package}")
        # 尝试不使用--user参数
        try:
            subprocess.run([
                sys.executable, "-m", "pip", "install", package
            ], check=True, capture_output=True)
            print(f"✅ {package} installed (system-wide)")
            return True
        except:
            print(f"❌ All installation methods failed for {package}")
            return False

def install_dependencies(build_type="release"):
    """安装所有依赖"""
    print(f"📚 Installing dependencies for {build_type} build...")

    # 基础包
    packages = [
        "pyinstaller",
        "DrissionPage",
        "requests",
        "urllib3",
        "certifi",
        "pywin32"
    ]

    # 如果是发布版本，添加卡密系统依赖
    if build_type == "release":
        license_packages = [
            "cryptography",
            "pycryptodome"
        ]
        packages.extend(license_packages)
        print("🔐 Adding license system dependencies...")

    success_count = 0
    for package in packages:
        if install_package(package):
            success_count += 1

    print(f"📊 Installed {success_count}/{len(packages)} packages")
    return success_count == len(packages)

def download_chrome_portable():
    """下载便携版Chrome"""
    print("🌐 Setting up Chrome...")

    chrome_dir = Path("Chrome")
    chrome_exe = chrome_dir / "chrome.exe"

    if chrome_exe.exists():
        print("✅ Chrome already exists")
        return True

    print("📋 Chrome setup options:")
    print("1. Auto-download (experimental)")
    print("2. Manual setup (recommended)")
    print("3. Skip Chrome (use system Chrome)")

    choice = input("Choose option (1/2/3): ").strip()

    if choice == "1":
        return auto_download_chrome()
    elif choice == "2":
        return manual_chrome_setup()
    else:
        print("⚠️  Skipping Chrome setup")
        return True

def auto_download_chrome():
    """自动下载Chrome（实验性）"""
    print("🔄 Attempting auto-download...")

    # 这里可以添加自动下载逻辑
    # 由于Chrome下载链接经常变化，这里提供手动指导
    print("❌ Auto-download not available")
    print("💡 Redirecting to manual setup...")
    return manual_chrome_setup()

def manual_chrome_setup():
    """手动Chrome设置指导"""
    print("📋 Manual Chrome Setup:")
    print("1. Download Chrome standalone:")
    print("   https://www.google.com/chrome/browser/desktop/index.html?standalone=1")
    print("2. Extract to ./Chrome/ directory")
    print("3. Ensure Chrome/chrome.exe exists")

    input("Press Enter when Chrome is ready (or Ctrl+C to skip)...")

    chrome_exe = Path("Chrome/chrome.exe")
    if chrome_exe.exists():
        print("✅ Chrome setup complete")
        return True
    else:
        print("⚠️  Chrome not found, will use system Chrome")
        return True


def check_license_system(build_type):
    """检查卡密系统"""
    print(f"🔐 Checking license system for {build_type} build...")

    license_dir = Path("license_system")

    if build_type == "release":
        if not license_dir.exists():
            print("❌ Error: Release build requires license system")
            print("💡 License system directory not found: license_system/")
            return False

        # 检查必要文件
        required_files = [
            "__init__.py",
            "license_manager.py",
            "license_interface.py",
            "crypto_utils.py",
            "hardware_info.py",
            "ui_integration.py",
            "config.py",
            "exceptions.py"
        ]

        missing_files = []
        for file in required_files:
            if not (license_dir / file).exists():
                missing_files.append(file)

        if missing_files:
            print(f"❌ Missing license system files: {', '.join(missing_files)}")
            return False

        print("✅ License system ready for release build")
        return True

    else:  # dev build
        if license_dir.exists():
            print("ℹ️  License system found but will not be included in dev build")
        else:
            print("ℹ️  No license system (dev build)")
        return True

def create_build_config(build_type="release"):
    """创建构建配置"""
    exe_name = f"拼多多采集工具_{build_type}" if build_type == "dev" else "拼多多采集工具"

    config = {
        "name": exe_name,
        "build_type": build_type,
        "main_script": "main.py",
        "include_chrome": os.path.exists("Chrome"),
        "include_license": build_type == "release" and os.path.exists("license_system"),
        "hidden_imports": [
            "DrissionPage",
            "DrissionPage.common",
            "tkinter",
            "tkinter.ttk",
            "tkinter.messagebox",
            "tkinter.filedialog",
            "requests",
            "json",
            "threading",
            "queue",
            "datetime",
            "hashlib",
            "base64"
        ]
    }

    # 如果包含卡密系统，添加相关导入
    if config["include_license"]:
        license_imports = [
            "license_system",
            "license_system.license_manager",
            "license_system.license_interface",
            "license_system.crypto_utils",
            "license_system.hardware_info",
            "license_system.ui_integration",
            "license_system.config",
            "license_system.exceptions",
            "cryptography",
            "cryptography.fernet",
            "cryptography.hazmat.primitives"
        ]
        config["hidden_imports"].extend(license_imports)

    config_file = f"build_config_{build_type}.json"
    with open(config_file, "w", encoding="utf-8") as f:
        import json
        json.dump(config, f, indent=2, ensure_ascii=False)

    print(f"✅ Build config created: {config_file}")
    return config

def build_executable(config):
    """构建可执行文件"""
    build_type = config.get("build_type", "release")
    print(f"🔨 Building executable ({build_type})...")

    # 清理旧文件
    for dir_name in ["dist", "build"]:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)

    # 设置环境变量
    env = os.environ.copy()
    env["BUILD_TYPE"] = build_type

    # 构建命令
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",
        "--windowed",
        "--name", config["name"],
        "--collect-submodules=DrissionPage",
        "--collect-data=DrissionPage"
    ]

    # 添加数据文件
    data_files = [
        "core;core",
        "gui;gui"
    ]

    # 如果有Chrome，添加到打包中
    if config["include_chrome"]:
        data_files.append("Chrome;Chrome")
        print("📦 Including Chrome in build")

    # 如果包含卡密系统，添加到打包中
    if config["include_license"]:
        data_files.append("license_system;license_system")
        print("🔐 Including license system in build")

    for data_file in data_files:
        cmd.extend(["--add-data", data_file])

    # 添加隐藏导入
    for module in config["hidden_imports"]:
        cmd.extend(["--hidden-import", module])

    cmd.append(config["main_script"])

    try:
        print("⏳ Building... (this may take several minutes)")
        result = subprocess.run(cmd, check=True, capture_output=True, text=True, env=env)
        print(f"✅ Build successful! ({build_type})")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Build failed! ({build_type})")
        print("Error output:", e.stderr)
        return False

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="PDD Spider Auto Setup & Build")

    parser.add_argument("--skip-deps", action="store_true",
                       help="Skip dependency installation")
    parser.add_argument("--skip-chrome", action="store_true",
                       help="Skip Chrome setup")

    args = parser.parse_args()


    print("🚀 PDD Spider Auto Setup & Build")
    print("=" * 50)

    # 检查Python
    if not check_python():
        print("❌ Python environment not suitable")
        input("Press Enter to exit...")
        return

    # 确保pip可用
    if not ensure_pip():
        print("❌ pip setup failed")
        input("Press Enter to exit...")
        return

    print("📋 Versions setup options:")
    print("1. Release")
    print("2. Dev")

    choice = input("Choose option (1/2/): ").strip()

    if choice == '1':
        build_type = 'release'
    if choice == '2':
        build_type = 'dev'

    print(f"\n🔧 Processing {build_type} build...")
    print("-" * 30)

    # 检查卡密系统
    if not check_license_system(build_type):
        if build_type == "Release":
            print("❌ Cannot proceed with release build")

    # 安装依赖
    if not args.skip_deps:
        if not install_dependencies(build_type):
            print("⚠️  Some dependencies failed to install")
            choice = input("Continue anyway? (y/n): ")
            if choice.lower() != 'y':
                pass

    # 设置Chrome
    if not args.skip_chrome:
        download_chrome_portable()

    # 创建构建配置
    config = create_build_config(build_type)

    # 构建可执行文件
    if build_executable(config):
        exe_path = f"dist/{config['name']}.exe"
        if os.path.exists(exe_path):
            size = os.path.getsize(exe_path)
            print(f"\n🎉 {build_type.title()} build success!")
            print(f"📁 File: {exe_path}")
            print(f"📊 Size: {size/1024/1024:.1f} MB")
        else:
            print(f"⚠️  {build_type} executable not found")
    else:
        print(f"❌ {build_type} build process failed")

    print("\n" + "=" * 50)
    print("🏁 All builds completed!")
    input("Press Enter to exit...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n❌ Process interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        input("Press Enter to exit...")
