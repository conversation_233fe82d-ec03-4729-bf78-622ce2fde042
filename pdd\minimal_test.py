import sys
import os
sys.path.insert(0, '.')

print("开始测试")

try:
    print("导入模块...")
    import license_system.crypto_utils as crypto
    print("导入成功")
    
    print("生成卡密...")
    key = crypto.LicenseGenerator.generate_license(
        user_id="TEST",
        license_type="personal",
        duration_days=30,
        features=["basic"],
        hardware_fingerprint=None
    )
    print(f"卡密: {key}")
    
    print("解码卡密...")
    data = crypto.CryptoUtils.decode_license_data(key)
    print(f"用户ID: {data['user_id']}")
    
    print("测试成功！")
    
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()

print("测试结束")
