#!/usr/bin/env python3
"""
测试版本选择功能
"""
import subprocess
import sys

def test_version_selection():
    """测试版本选择功能"""
    print("🧪 测试版本选择功能")
    print("=" * 40)
    
    print("\n📋 测试场景:")
    
    print("\n1. 命令行指定版本:")
    print("   python auto_spider.py --type release")
    print("   python auto_spider.py --type dev")
    print("   python auto_spider.py --type both")
    
    print("\n2. 自动模式:")
    print("   python auto_spider.py --auto")
    
    print("\n3. 交互式选择:")
    print("   python auto_spider.py")
    print("   (会提示选择版本)")
    
    print("\n✨ 新增功能:")
    print("✅ 交互式版本选择")
    print("✅ Release/Dev版本区分")
    print("✅ 命令行参数支持")
    print("✅ 自动模式支持")
    
    print("\n🎯 使用建议:")
    print("- 开发时使用: python auto_spider.py --type dev")
    print("- 发布时使用: python auto_spider.py --type release")
    print("- 快速构建: python auto_spider.py --auto")

if __name__ == "__main__":
    test_version_selection()
