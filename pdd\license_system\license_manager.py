"""
卡密管理核心模块
"""
import json
import os
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List

try:
    from .config import LICENSE_FILE, HARDWARE_FILE, LICENSE_TYPES, FEATURES, TRIAL_MODE, VERIFICATION
    from .crypto_utils import CryptoUtils, LicenseGenerator
    from .hardware_info import HardwareInfo
    from .exceptions import (
        LicenseError, LicenseInvalidError, LicenseExpiredError,
        LicenseHardwareError, LicenseFeatureError
    )
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    from license_system.config import LICENSE_FILE, HARDWARE_FILE, LICENSE_TYPES, FEATURES, TRIAL_MODE, VERIFICATION
    from license_system.crypto_utils import CryptoUtils, LicenseGenerator
    from license_system.hardware_info import HardwareInfo
    from license_system.exceptions import (
        LicenseError, LicenseInvalidError, LicenseExpiredError,
        LicenseHardwareError, LicenseFeatureError
    )


class LicenseManager:
    """卡密管理器"""
    
    _instance = None
    _license_data = None
    _last_check_time = 0
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    @classmethod
    def verify_startup(cls) -> bool:
        """启动时验证卡密"""
        try:
            manager = cls()
            return manager._verify_license()
        except Exception as e:
            print(f"启动验证失败: {e}")
            return False
    
    @classmethod
    def check_feature(cls, feature: str) -> bool:
        """检查功能权限"""
        try:
            manager = cls()
            return manager._check_feature_permission(feature)
        except Exception:
            return False
    
    @classmethod
    def get_status(cls) -> Dict[str, Any]:
        """获取授权状态"""
        try:
            manager = cls()
            return manager._get_license_status()
        except Exception as e:
            return {
                "status": "error",
                "message": f"获取状态失败: {e}",
                "valid": False
            }
    
    @classmethod
    def activate_license(cls, license_key: str) -> bool:
        """激活卡密"""
        try:
            manager = cls()
            return manager._activate_license(license_key)
        except Exception as e:
            print(f"激活失败: {e}")
            return False
    
    @classmethod
    def generate_trial(cls) -> bool:
        """生成试用卡密"""
        try:
            if not TRIAL_MODE["enabled"]:
                return False
            
            # 检查是否已经使用过试用
            if os.path.exists(LICENSE_FILE):
                return False
            
            # 获取硬件指纹
            hardware_fp = HardwareInfo.get_hardware_fingerprint()
            
            # 生成试用卡密
            trial_license = LicenseGenerator.generate_trial_license(hardware_fp)
            
            # 激活试用卡密
            manager = cls()
            return manager._activate_license(trial_license)
            
        except Exception as e:
            print(f"生成试用卡密失败: {e}")
            return False
    
    def _verify_license(self) -> bool:
        """内部验证方法"""
        # 检查是否需要重新验证
        current_time = time.time()
        if (self._license_data and 
            current_time - self._last_check_time < VERIFICATION["check_interval"]):
            return True
        
        # 加载卡密
        if not self._load_license():
            return False
        
        # 验证卡密
        if not self._validate_license():
            return False
        
        # 更新检查时间
        self._last_check_time = current_time
        return True
    
    def _load_license(self) -> bool:
        """加载卡密文件"""
        try:
            if not os.path.exists(LICENSE_FILE):
                return False
            
            with open(LICENSE_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 解码卡密数据
            license_key = data.get("license_key", "")
            self._license_data = CryptoUtils.decode_license_data(license_key)
            
            return True
            
        except Exception as e:
            print(f"加载卡密失败: {e}")
            return False
    
    def _validate_license(self) -> bool:
        """验证卡密有效性"""
        if not self._license_data:
            return False

        try:
            # 检查卡密状态（从数据库）
            if self._check_license_status():
                # 如果卡密被禁用，直接返回失败
                return False

            # 验证时间
            if VERIFICATION["time_check"]:
                expire_time = datetime.fromisoformat(self._license_data["expire_time"])
                if datetime.now() > expire_time:
                    raise LicenseExpiredError("卡密已过期")

            # 验证硬件绑定
            if VERIFICATION["hardware_binding"]:
                saved_fp = self._license_data.get("hardware_fingerprint")
                if saved_fp:
                    current_fp = HardwareInfo.get_hardware_fingerprint()
                    if not HardwareInfo.verify_hardware_binding(saved_fp):
                        raise LicenseHardwareError("硬件绑定验证失败")

            # 验证签名 (简化版本)
            user_id = self._license_data["user_id"]
            license_type = self._license_data["license_type"]
            expire_time = self._license_data["expire_time"]
            signature_data = f"{user_id}|{license_type}|{expire_time}"
            expected_signature = CryptoUtils.generate_checksum(signature_data)[:16]

            if self._license_data.get("signature") != expected_signature:
                raise LicenseInvalidError("卡密签名验证失败")

            return True
            
        except LicenseError:
            raise
        except Exception as e:
            raise LicenseInvalidError(f"卡密验证失败: {e}")

    def _check_license_status(self) -> bool:
        """检查卡密状态（从数据库）
        返回True表示卡密被禁用，False表示正常
        """
        if not self._license_data:
            return True  # 没有卡密数据，视为禁用

        try:
            import sqlite3
            import os

            # 查找卡密生成器的数据库
            db_paths = [
                "license_generator/license_data.db",
                "license_data.db",
                "../license_generator/license_data.db"
            ]

            db_path = None
            for path in db_paths:
                if os.path.exists(path):
                    db_path = path
                    break

            if not db_path:
                # 如果找不到数据库，假设卡密正常（向后兼容）
                return False

            # 连接数据库检查状态
            conn = sqlite3.connect(db_path)
            cursor = conn.execute(
                "SELECT status FROM licenses WHERE user_id = ? OR license_key LIKE ?",
                (self._license_data.get("user_id", ""), f"%{self._license_data.get('user_id', '')}%")
            )

            row = cursor.fetchone()
            conn.close()

            if row:
                status = row[0]
                if status == 'disabled':
                    raise LicenseInvalidError("卡密已被禁用")
                return False  # 状态正常
            else:
                # 数据库中找不到对应卡密，可能是新生成的
                return False

        except LicenseError:
            raise
        except Exception as e:
            # 数据库检查失败，假设卡密正常（向后兼容）
            print(f"状态检查失败: {e}")
            return False

    def _check_feature_permission(self, feature: str) -> bool:
        """检查功能权限"""
        if not self._verify_license():
            return False
        
        if not self._license_data:
            return False
        
        # 检查功能是否在授权列表中
        licensed_features = self._license_data.get("features", [])
        return feature in licensed_features
    
    def _get_license_status(self) -> Dict[str, Any]:
        """获取卡密状态"""
        if not self._license_data:
            if not self._load_license():
                return {
                    "status": "no_license",
                    "message": "未找到有效卡密",
                    "valid": False
                }
        
        try:
            self._validate_license()
            
            # 计算剩余天数
            expire_time = datetime.fromisoformat(self._license_data["expire_time"])
            remaining_days = (expire_time - datetime.now()).days
            
            license_type = self._license_data.get("license_type", "unknown")
            type_info = LICENSE_TYPES.get(license_type, {"name": "未知类型"})
            
            return {
                "status": "valid",
                "message": f"授权有效 - {type_info['name']}",
                "valid": True,
                "license_type": license_type,
                "type_name": type_info["name"],
                "user_id": self._license_data.get("user_id", ""),
                "expire_time": self._license_data["expire_time"],
                "remaining_days": remaining_days,
                "features": self._license_data.get("features", [])
            }
            
        except LicenseExpiredError:
            return {
                "status": "expired",
                "message": "卡密已过期",
                "valid": False
            }
        except LicenseError as e:
            return {
                "status": "invalid",
                "message": f"卡密无效: {e}",
                "valid": False
            }
    
    def _activate_license(self, license_key: str) -> bool:
        """激活卡密"""
        try:
            # 解码卡密
            license_data = CryptoUtils.decode_license_data(license_key)
            
            # 验证卡密格式
            required_fields = ["version", "user_id", "license_type", "expire_time", "features"]
            for field in required_fields:
                if field not in license_data:
                    raise LicenseInvalidError(f"卡密缺少必要字段: {field}")
            
            # 验证过期时间
            expire_time = datetime.fromisoformat(license_data["expire_time"])
            if datetime.now() > expire_time:
                raise LicenseExpiredError("卡密已过期")
            
            # 绑定硬件
            if VERIFICATION["hardware_binding"]:
                current_fp = HardwareInfo.get_hardware_fingerprint()
                saved_fp = license_data.get("hardware_fingerprint")
                
                if saved_fp and saved_fp != current_fp:
                    # 检查是否允许重新绑定
                    if not HardwareInfo.verify_hardware_binding(saved_fp):
                        raise LicenseHardwareError("卡密已绑定其他设备")
                else:
                    # 首次激活，绑定当前设备
                    license_data["hardware_fingerprint"] = current_fp
            
            # 保存卡密
            license_info = {
                "license_key": license_key,
                "activate_time": datetime.now().isoformat(),
                "hardware_fingerprint": HardwareInfo.get_hardware_fingerprint()
            }
            
            os.makedirs(os.path.dirname(LICENSE_FILE), exist_ok=True)
            with open(LICENSE_FILE, 'w', encoding='utf-8') as f:
                json.dump(license_info, f, indent=2, ensure_ascii=False)
            
            # 保存硬件信息
            HardwareInfo.save_hardware_info(HARDWARE_FILE)
            
            # 更新内存中的数据
            self._license_data = license_data
            self._last_check_time = time.time()
            
            return True
            
        except LicenseError:
            raise
        except Exception as e:
            raise LicenseInvalidError(f"激活失败: {e}")
    
    @classmethod
    def deactivate_license(cls) -> bool:
        """停用卡密"""
        try:
            if os.path.exists(LICENSE_FILE):
                os.remove(LICENSE_FILE)
            if os.path.exists(HARDWARE_FILE):
                os.remove(HARDWARE_FILE)
            
            # 清除内存数据
            manager = cls()
            manager._license_data = None
            manager._last_check_time = 0
            
            return True
        except Exception as e:
            print(f"停用卡密失败: {e}")
            return False
