"""
打包脚本 - 将GUI程序打包为可执行文件
支持开发版和发布版的不同构建
"""
import os
import sys
import subprocess
import shutil
import argparse
from pathlib import Path


def clean_build_dirs():
    """清理构建目录"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"已清理目录: {dir_name}")
    
    # 清理.spec文件
    spec_files = [f for f in os.listdir('.') if f.endswith('.spec')]
    for spec_file in spec_files:
        os.remove(spec_file)
        print(f"已删除文件: {spec_file}")


def create_icon():
    """创建应用图标（如果不存在）"""
    icon_path = "icon.ico"
    if not os.path.exists(icon_path):
        print("未找到图标文件，将使用默认图标")
        return None
    return icon_path


def check_chrome():
    """检查Chrome浏览器"""
    chrome_path = Path("Chrome/chrome.exe")
    if not chrome_path.exists():
        print("⚠️  警告：未找到 Chrome/chrome.exe")
        print("建议下载便携版Chrome到Chrome目录以确保程序在所有电脑上正常运行")
        print("下载地址：https://www.google.com/chrome/browser/desktop/index.html?standalone=1")
        return False
    else:
        print("✅ 检测到Chrome浏览器，将一起打包")
        return True


def check_license_system(build_type):
    """检查卡密系统"""
    license_path = Path("license_system")

    if build_type == "release":
        if not license_path.exists():
            print("❌ 错误：发布版本需要卡密系统，但未找到 license_system 目录")
            return False
        else:
            print("✅ 检测到卡密系统，将集成到发布版本")
            return True
    else:
        if license_path.exists():
            print("ℹ️  检测到卡密系统，但开发版本不会集成")
        return False

def build_executable(build_type="release"):
    """构建可执行文件"""
    print(f"开始构建可执行文件 ({build_type})...")

    # 检查Chrome
    has_chrome = check_chrome()

    # 检查卡密系统
    has_license_system = check_license_system(build_type)

    # PyInstaller命令参数
    exe_name = f"拼多多采集工具_{build_type}" if build_type == "dev" else "拼多多采集工具"
    cmd = [
        'pyinstaller',
        '--onefile',                    # 打包成单个文件
        '--windowed',                   # 不显示控制台窗口
        f'--name={exe_name}',           # 可执行文件名称
        '--distpath=dist',              # 输出目录
        '--workpath=build',             # 工作目录
        '--clean',                      # 清理临时文件
        '--noconfirm',                  # 不询问确认
    ]

    # 设置环境变量
    env = os.environ.copy()
    env["BUILD_TYPE"] = build_type

    # 添加图标
    icon_path = create_icon()
    if icon_path:
        cmd.extend(['--icon', icon_path])

    # 添加数据文件
    data_files = [
        'config.json;.',
        'README.md;.',
        'core;core',
        'gui;gui',
    ]

    # 如果有Chrome，添加到打包中
    if has_chrome:
        data_files.append('Chrome;Chrome')
        print("📦 将Chrome浏览器打包到程序中")

    # 如果是发布版本且有卡密系统，添加到打包中
    if has_license_system:
        data_files.append('license_system;license_system')
        print("🔐 将卡密系统打包到程序中")

    for data_file in data_files:
        cmd.extend(['--add-data', data_file])
    
    # 隐藏导入
    hidden_imports = [
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.filedialog',
        'pandas',
        'openpyxl',
        'DrissionPage',
        'DrissionPage.common',
        'requests',
        'json',
        'threading',
        'queue',
        'datetime',
        'os',
        'sys',
        're',
        'time',
        'random',
        'pathlib',
        'subprocess',
        'shutil'
    ]

    # 如果是发布版本，添加卡密系统相关导入
    if has_license_system:
        license_imports = [
            'license_system',
            'license_system.license_manager',
            'license_system.license_interface',
            'license_system.crypto_utils',
            'license_system.hardware_info',
            'license_system.ui_integration',
            'cryptography',
            'cryptography.fernet',
            'cryptography.hazmat.primitives',
            'hashlib',
            'base64'
        ]
        hidden_imports.extend(license_imports)
    
    for module in hidden_imports:
        cmd.extend(['--hidden-import', module])
    
    # 主程序文件
    cmd.append('main.py')
    
    # 执行打包命令
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True, env=env)
        print(f"打包成功！({build_type})")
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"打包失败！({build_type})")
        print(e.stderr)
        return False


def create_installer_script():
    """创建安装脚本"""
    installer_script = """
@echo off
echo 拼多多商品数据采集工具安装程序
echo ================================

echo 正在创建程序目录...
if not exist "C:\\PddSpider" mkdir "C:\\PddSpider"

echo 正在复制程序文件...
copy "拼多多采集工具.exe" "C:\\PddSpider\\"
copy "config.json" "C:\\PddSpider\\" 2>nul
copy "README.md" "C:\\PddSpider\\" 2>nul

echo 正在创建桌面快捷方式...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\\Desktop\\拼多多采集工具.lnk'); $Shortcut.TargetPath = 'C:\\PddSpider\\拼多多采集工具.exe'; $Shortcut.Save()"

echo 安装完成！
echo 程序已安装到: C:\\PddSpider\\
echo 桌面快捷方式已创建
pause
    """
    
    with open('dist/install.bat', 'w', encoding='gbk') as f:
        f.write(installer_script)
    
    print("已创建安装脚本: dist/install.bat")


def create_readme():
    """创建发布说明"""
    readme_content = """
# 拼多多商品数据采集工具

## 安装说明

### 方法一：直接运行
1. 双击 `拼多多采集工具.exe` 直接运行程序

### 方法二：安装到系统
1. 双击 `install.bat` 运行安装程序
2. 程序将安装到 `C:\\PddSpider\\` 目录
3. 桌面将创建快捷方式

## 使用说明

### 首次使用
1. 启动程序后，切换到"账号登录"标签页
2. 点击"开始登录"按钮
3. 在弹出的对话框中输入手机号和验证码
4. 登录成功后即可开始采集

### 数据采集
1. 切换到"数据采集"标签页
2. 选择采集模式：
   - 批量采集：设置页数，自动采集多页商品
   - 单品采集：输入商品ID进行单个商品采集
3. 点击"开始采集"按钮
4. 在日志区域查看采集进度

### 数据管理
1. 切换到"数据管理"标签页
2. 查看已采集的商品数据
3. 选择导出格式（CSV/Excel/JSON）
4. 点击"导出数据"按钮导出数据

### 系统设置
1. 切换到"系统设置"标签页
2. 调整采集参数：延时时间、重试次数等
3. 设置文件保存路径
4. 点击"保存设置"保存配置

## 注意事项

1. **账号安全**：不要频繁使用同一账号采集大量数据
2. **采集频率**：建议控制采集频率，避免IP被封
3. **数据备份**：定期备份采集到的数据
4. **法律合规**：采集的数据仅限个人研究使用
5. **浏览器**：程序已内置Chrome浏览器，无需额外安装

## 技术支持

如遇到问题，请检查：
1. 网络连接是否正常
2. 是否已正确登录账号
3. 防火墙是否阻止程序运行
4. 杀毒软件是否误报

## 免责声明

本工具仅供学习和研究使用，请遵守相关法律法规和网站的使用条款，
不得用于任何商业或非法用途。使用本工具所产生的任何后果由使用者自行承担。
    """
    
    with open('dist/使用说明.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("已创建使用说明: dist/使用说明.txt")


def main():
    """主函数"""
    print("拼多多采集工具打包程序")
    print("=" * 40)
    
    # 检查是否安装了PyInstaller
    try:
        subprocess.run(['pyinstaller', '--version'], check=True, capture_output=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("错误：未安装PyInstaller")
        print("请运行：pip install pyinstaller")
        return False
    
    # 清理构建目录
    clean_build_dirs()
    
    # 构建可执行文件
    if not build_executable():
        return False
    
    # 创建安装脚本
    create_installer_script()
    
    # 创建使用说明
    create_readme()
    
    print("\n" + "=" * 40)
    print("打包完成！")
    print("输出目录: dist/")
    print("可执行文件: dist/拼多多采集工具.exe")
    print("安装脚本: dist/install.bat")
    print("使用说明: dist/使用说明.txt")
    print("=" * 40)
    
    return True


if __name__ == "__main__":
    success = main()
    if not success:
        input("按任意键退出...")
        sys.exit(1)
