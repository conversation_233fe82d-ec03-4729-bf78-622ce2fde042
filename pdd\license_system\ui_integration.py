"""
UI集成模块
提供卡密系统的UI组件
"""
import tkinter as tk
from tkinter import ttk, messagebox
import threading

try:
    from .license_interface import LicenseInterface
    from .exceptions import LicenseError
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    from license_system.license_interface import LicenseInterface
    from license_system.exceptions import LicenseError


class LicenseDialog:
    """卡密激活对话框"""
    
    def __init__(self, parent):
        self.parent = parent
        self.result = False
        self.dialog = None
        
    def show(self) -> bool:
        """显示卡密对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("软件授权")
        self.dialog.geometry("500x400")
        self.dialog.resizable(False, False)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # 居中显示
        self._center_window()
        
        # 创建界面
        self._create_widgets()
        
        # 等待对话框关闭
        self.dialog.wait_window()
        
        return self.result
    
    def _center_window(self):
        """窗口居中"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (400 // 2)
        self.dialog.geometry(f"500x400+{x}+{y}")
    
    def _create_widgets(self):
        """创建界面组件"""
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🔐 软件授权验证", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 状态显示
        status = LicenseInterface.get_status()
        status_frame = ttk.LabelFrame(main_frame, text="当前状态", padding=10)
        status_frame.pack(fill=tk.X, pady=5)
        
        status_text = f"状态: {status['message']}"
        ttk.Label(status_frame, text=status_text).pack(anchor=tk.W)
        
        # 卡密输入
        license_frame = ttk.LabelFrame(main_frame, text="卡密激活", padding=10)
        license_frame.pack(fill=tk.X, pady=10)
        
        ttk.Label(license_frame, text="请输入卡密:").pack(anchor=tk.W)
        
        self.license_entry = ttk.Entry(license_frame, font=("Courier", 12), width=40)
        self.license_entry.pack(fill=tk.X, pady=5)
        self.license_entry.bind('<KeyRelease>', self._on_license_change)
        
        # 格式提示
        format_label = ttk.Label(license_frame, text="格式: XXXX-XXXX-XXXX-XXXX-XXXX", 
                                foreground="gray", font=("Arial", 9))
        format_label.pack(anchor=tk.W)
        
        # 试用选项
        trial_frame = ttk.LabelFrame(main_frame, text="试用选项", padding=10)
        trial_frame.pack(fill=tk.X, pady=10)
        
        trial_text = "如果您还没有卡密，可以申请7天免费试用"
        ttk.Label(trial_frame, text=trial_text).pack(anchor=tk.W)
        
        self.trial_btn = ttk.Button(trial_frame, text="申请试用", 
                                   command=self._apply_trial)
        self.trial_btn.pack(anchor=tk.W, pady=5)
        
        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=20)
        
        self.activate_btn = ttk.Button(button_frame, text="激活", 
                                      command=self._activate_license, 
                                      state=tk.DISABLED)
        self.activate_btn.pack(side=tk.RIGHT, padx=5)
        
        ttk.Button(button_frame, text="取消", 
                  command=self._cancel).pack(side=tk.RIGHT, padx=5)
        
        # 说明文本
        info_text = """
使用说明:
1. 输入完整的卡密进行激活
2. 卡密格式为: XXXX-XXXX-XXXX-XXXX-XXXX
3. 首次使用可申请7天免费试用
4. 如需购买正式卡密，请联系客服

联系方式:
- QQ: 123456789
- 微信: pdd_spider
- 邮箱: <EMAIL>
        """
        
        info_label = ttk.Label(main_frame, text=info_text, 
                              font=("Arial", 9), foreground="gray")
        info_label.pack(pady=10)
    
    def _on_license_change(self, event=None):
        """卡密输入变化处理"""
        license_key = self.license_entry.get().strip()
        
        # 自动格式化
        if len(license_key) > 0 and '-' not in license_key:
            formatted = '-'.join([license_key[i:i+4] for i in range(0, len(license_key), 4)])
            if formatted != license_key:
                self.license_entry.delete(0, tk.END)
                self.license_entry.insert(0, formatted)
                return
        
        # 检查格式
        if len(license_key) == 24 and license_key.count('-') == 4:
            self.activate_btn.config(state=tk.NORMAL)
        else:
            self.activate_btn.config(state=tk.DISABLED)
    
    def _activate_license(self):
        """激活卡密"""
        license_key = self.license_entry.get().strip()
        
        if not license_key:
            messagebox.showerror("错误", "请输入卡密")
            return
        
        # 在新线程中激活
        def activate_thread():
            try:
                success = LicenseInterface.activate_license(license_key)
                self.dialog.after(0, self._activate_callback, success)
            except Exception as e:
                self.dialog.after(0, self._activate_error, str(e))
        
        threading.Thread(target=activate_thread, daemon=True).start()
        
        # 禁用按钮
        self.activate_btn.config(state=tk.DISABLED, text="激活中...")
    
    def _activate_callback(self, success: bool):
        """激活回调"""
        self.activate_btn.config(state=tk.NORMAL, text="激活")
        
        if success:
            messagebox.showinfo("成功", "卡密激活成功！")
            self.result = True
            self.dialog.destroy()
        else:
            messagebox.showerror("失败", "卡密激活失败，请检查卡密是否正确")
    
    def _activate_error(self, error_msg: str):
        """激活错误"""
        self.activate_btn.config(state=tk.NORMAL, text="激活")
        messagebox.showerror("错误", f"激活过程出错: {error_msg}")
    
    def _apply_trial(self):
        """申请试用"""
        if messagebox.askyesno("确认", "确定要申请7天免费试用吗？\n每台设备只能申请一次试用。"):
            try:
                success = LicenseInterface.generate_trial()
                if success:
                    messagebox.showinfo("成功", "试用激活成功！您可以免费使用7天。")
                    self.result = True
                    self.dialog.destroy()
                else:
                    messagebox.showerror("失败", "试用申请失败，可能您已经使用过试用版本。")
            except Exception as e:
                messagebox.showerror("错误", f"申请试用出错: {e}")
    
    def _cancel(self):
        """取消"""
        self.result = False
        self.dialog.destroy()


class LicenseStatusWidget:
    """卡密状态显示组件"""
    
    def __init__(self, parent):
        self.parent = parent
        self.status_var = tk.StringVar()
        self.create_widget()
        self.update_status()
    
    def create_widget(self):
        """创建状态显示组件"""
        self.frame = ttk.Frame(self.parent)
        
        # 状态标签
        self.status_label = ttk.Label(self.frame, textvariable=self.status_var,
                                     font=("Arial", 9), foreground="gray")
        self.status_label.pack(side=tk.LEFT)
        
        # 管理按钮
        if LicenseInterface.is_enabled():
            self.manage_btn = ttk.Button(self.frame, text="授权管理", 
                                        command=self.show_license_dialog)
            self.manage_btn.pack(side=tk.RIGHT)
    
    def update_status(self):
        """更新状态显示"""
        status = LicenseInterface.get_status()
        
        if status['valid']:
            if status['status'] == 'dev':
                self.status_var.set("开发模式")
            else:
                remaining = status.get('remaining_days', 0)
                self.status_var.set(f"授权: {status['type_name']} (剩余{remaining}天)")
        else:
            self.status_var.set("未授权")
    
    def show_license_dialog(self):
        """显示授权对话框"""
        dialog = LicenseDialog(self.parent)
        if dialog.show():
            self.update_status()
    
    def pack(self, **kwargs):
        """打包组件"""
        self.frame.pack(**kwargs)
    
    def grid(self, **kwargs):
        """网格布局"""
        self.frame.grid(**kwargs)


def show_license_dialog(parent) -> bool:
    """显示卡密对话框"""
    dialog = LicenseDialog(parent)
    return dialog.show()


def create_license_status_widget(parent) -> LicenseStatusWidget:
    """创建状态显示组件"""
    return LicenseStatusWidget(parent)
