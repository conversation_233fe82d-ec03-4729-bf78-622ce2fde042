@echo off
chcp 65001 >nul
echo [INFO] Building PDD Spider...

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python not found
    pause
    exit /b 1
)

REM 检查PyInstaller
python -c "import PyInstaller" >nul 2>&1
if errorlevel 1 (
    echo [INFO] Installing PyInstaller...
    pip install pyinstaller
)

REM 清理旧文件
if exist "dist" rmdir /s /q "dist" 2>nul
if exist "build" rmdir /s /q "build" 2>nul
if exist "*.spec" del /q "*.spec" 2>nul

echo [INFO] Starting build...

REM 打包程序
python -m PyInstaller --onefile --windowed --name "拼多读采集工具" --add-data "Chrome;Chrome" --hidden-import=DrissionPage --hidden-import=tkinter --hidden-import=requests --hidden-import=json --hidden-import=threading main.py

if errorlevel 1 (
    echo [ERROR] Build failed!
    pause
    exit /b 1
)

echo [SUCCESS] Build completed!
echo [INFO] File: dist\PDD_Spider.exe
pause
