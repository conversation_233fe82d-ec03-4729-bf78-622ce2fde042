"""
数据处理模块
"""
import json
import os
import pandas as pd
from typing import List, Dict, Any, Optional
from datetime import datetime


class DataProcessor:
    """数据处理类"""

    @staticmethod
    def _safe_price_convert(price_value) -> float:
        """
        安全地转换价格值

        Args:
            price_value: 价格值（可能是字符串、整数或None）

        Returns:
            float: 转换后的价格（元）
        """
        try:
            if price_value is None:
                return 0.0

            # 如果是字符串，尝试转换为数字
            if isinstance(price_value, str):
                if not price_value.strip():
                    return 0.0
                price_value = float(price_value)

            # 如果是数字，除以100转换为元
            if isinstance(price_value, (int, float)):
                return float(price_value) / 100

            return 0.0
        except (ValueError, TypeError, ZeroDivisionError):
            return 0.0

    @staticmethod
    def load_product(product_id: str, result_path: str = "./result/") -> Optional[Dict[str, Any]]:
        """
        加载商品数据
        
        Args:
            product_id: 商品ID
            result_path: 结果文件路径
            
        Returns:
            Dict: 商品数据，如果不存在返回None
        """
        try:
            filepath = os.path.join(result_path, f"{product_id}.json")
            if not os.path.exists(filepath):
                return None
                
            with open(filepath, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载商品数据失败 {product_id}: {str(e)}")
            return None
    
    @staticmethod
    def extract_product_info(product_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取商品关键信息
        
        Args:
            product_data: 原始商品数据
            
        Returns:
            Dict: 提取的关键信息
        """
        if not product_data or "store" not in product_data:
            return {}
            
        try:
            goods_data = product_data.get("store", {}).get("initDataObj", {}).get("goods", {})
            if not goods_data:
                return {}
                
            # 提取关键字段
            info = {
                "商品ID": goods_data.get("goodsID", ""),
                "商品名称": goods_data.get("goodsName", ""),
                "价格": DataProcessor._safe_price_convert(goods_data.get("minGroupPrice", 0)),
                "店铺名称": goods_data.get("mallName", ""),
                "分类ID": goods_data.get("catID", ""),
                "销量提示": goods_data.get("salesTip", ""),
                "品牌ID": goods_data.get("brandId", ""),
                "商品类型": goods_data.get("goodsType", ""),
                "服务器时间": goods_data.get("serverTime", ""),
                "主图数量": len(goods_data.get("topGallery", [])),
                "是否有本地团": goods_data.get("hasLocalGroup", 0),
                "状态说明": goods_data.get("statusExplain", ""),
            }
            
            # 提取图片链接
            top_gallery = goods_data.get("topGallery", [])
            if top_gallery:
                info["主图链接"] = [img.get("url", "") for img in top_gallery[:5]]  # 最多5张
            else:
                info["主图链接"] = []
                
            return info
            
        except Exception as e:
            print(f"提取商品信息失败: {str(e)}")
            return {}
    
    @staticmethod
    def load_all_ids(cache_path: str = "./cache/ids.json") -> Dict[str, str]:
        """
        加载所有已采集商品ID
        
        Args:
            cache_path: 缓存文件路径
            
        Returns:
            Dict: 商品ID到名称的映射
        """
        try:
            if not os.path.exists(cache_path):
                return {}
                
            with open(cache_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载商品ID缓存失败: {str(e)}")
            return {}
    
    @staticmethod
    def get_data_statistics(result_path: str = "./result/",
                          cache_path: str = "./cache/ids.json") -> Dict[str, Any]:
        """
        获取数据统计信息

        Args:
            result_path: 结果文件路径
            cache_path: 缓存文件路径

        Returns:
            Dict: 统计信息
        """
        try:
            # 统计结果文件数量
            result_count = 0
            latest_file_time = None
            oldest_file_time = None

            if os.path.exists(result_path):
                result_files = [f for f in os.listdir(result_path) if f.endswith('.json')]
                result_count = len(result_files)

                # 获取最新和最旧文件时间
                if result_files:
                    file_times = []
                    for filename in result_files:
                        filepath = os.path.join(result_path, filename)
                        if os.path.isfile(filepath):
                            mtime = os.path.getmtime(filepath)
                            file_times.append(mtime)

                    if file_times:
                        latest_file_time = datetime.fromtimestamp(max(file_times)).strftime("%Y-%m-%d %H:%M:%S")
                        oldest_file_time = datetime.fromtimestamp(min(file_times)).strftime("%Y-%m-%d %H:%M:%S")

            # 统计缓存中的商品数量
            cache_dict = DataProcessor.load_all_ids(cache_path)
            cache_count = len(cache_dict)

            # 计算文件夹大小
            total_size = 0
            if os.path.exists(result_path):
                for filename in os.listdir(result_path):
                    filepath = os.path.join(result_path, filename)
                    if os.path.isfile(filepath):
                        total_size += os.path.getsize(filepath)

            # 转换为MB
            size_mb = total_size / (1024 * 1024)

            # 计算平均文件大小
            avg_file_size = (total_size / result_count) if result_count > 0 else 0
            avg_file_size_kb = avg_file_size / 1024

            return {
                "result_files": result_count,
                "cached_products": cache_count,
                "total_size_mb": round(size_mb, 2),
                "avg_file_size_kb": round(avg_file_size_kb, 2),
                "latest_file_time": latest_file_time or "无",
                "oldest_file_time": oldest_file_time or "无",
                "last_update": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "data_consistency": result_count == cache_count  # 数据一致性检查
            }
            
        except Exception as e:
            print(f"获取统计信息失败: {str(e)}")
            return {
                "result_files": 0,
                "cached_products": 0,
                "total_size_mb": 0,
                "avg_file_size_kb": 0,
                "latest_file_time": "未知",
                "oldest_file_time": "未知",
                "last_update": "未知",
                "data_consistency": False
            }
    
    @staticmethod
    def export_to_csv(product_ids: List[str], output_file: str, 
                     result_path: str = "./result/") -> bool:
        """
        导出商品数据到CSV
        
        Args:
            product_ids: 商品ID列表
            output_file: 输出文件路径
            result_path: 结果文件路径
            
        Returns:
            bool: 是否导出成功
        """
        try:
            data = []
            for pid in product_ids:
                product_data = DataProcessor.load_product(pid, result_path)
                if product_data:
                    info = DataProcessor.extract_product_info(product_data)
                    if info:
                        # 处理图片链接列表
                        if "主图链接" in info and isinstance(info["主图链接"], list):
                            info["主图链接"] = "; ".join(info["主图链接"])
                        data.append(info)
                        
            if not data:
                print("没有数据可导出")
                return False
                
            # 创建DataFrame并导出
            df = pd.DataFrame(data)
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            
            df.to_csv(output_file, index=False, encoding='utf-8-sig')
            print(f"数据已导出到 {output_file}")
            return True
            
        except Exception as e:
            print(f"导出CSV失败: {str(e)}")
            return False
        
    @staticmethod
    def export_to_excel(product_ids: List[str], output_file: str, 
                       result_path: str = "./result/") -> bool:
        """
        导出商品数据到Excel
        
        Args:
            product_ids: 商品ID列表
            output_file: 输出文件路径
            result_path: 结果文件路径
            
        Returns:
            bool: 是否导出成功
        """
        try:
            data = []
            for pid in product_ids:
                product_data = DataProcessor.load_product(pid, result_path)
                if product_data:
                    info = DataProcessor.extract_product_info(product_data)
                    if info:
                        # 处理图片链接列表
                        if "主图链接" in info and isinstance(info["主图链接"], list):
                            info["主图链接"] = "; ".join(info["主图链接"])
                        data.append(info)
                        
            if not data:
                print("没有数据可导出")
                return False
                
            # 创建DataFrame并导出
            df = pd.DataFrame(data)
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            
            df.to_excel(output_file, index=False, engine='openpyxl')
            print(f"数据已导出到 {output_file}")
            return True
            
        except Exception as e:
            print(f"导出Excel失败: {str(e)}")
            return False
    
    @staticmethod
    def export_to_json(product_ids: List[str], output_file: str, 
                      result_path: str = "./result/") -> bool:
        """
        导出商品数据到JSON
        
        Args:
            product_ids: 商品ID列表
            output_file: 输出文件路径
            result_path: 结果文件路径
            
        Returns:
            bool: 是否导出成功
        """
        try:
            data = []
            for pid in product_ids:
                product_data = DataProcessor.load_product(pid, result_path)
                if product_data:
                    info = DataProcessor.extract_product_info(product_data)
                    if info:
                        data.append(info)
                        
            if not data:
                print("没有数据可导出")
                return False
                
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
            print(f"数据已导出到 {output_file}")
            return True
            
        except Exception as e:
            print(f"导出JSON失败: {str(e)}")
            return False
    
    @staticmethod
    def delete_product_data(product_id: str, result_path: str = "./result/", 
                          cache_path: str = "./cache/ids.json") -> bool:
        """
        删除商品数据
        
        Args:
            product_id: 商品ID
            result_path: 结果文件路径
            cache_path: 缓存文件路径
            
        Returns:
            bool: 是否删除成功
        """
        try:
            # 删除结果文件
            result_file = os.path.join(result_path, f"{product_id}.json")
            if os.path.exists(result_file):
                os.remove(result_file)
                
            # 从缓存中删除
            cache_dict = DataProcessor.load_all_ids(cache_path)
            if product_id in cache_dict:
                del cache_dict[product_id]
                with open(cache_path, 'w', encoding='utf-8') as f:
                    json.dump(cache_dict, f, indent=2, ensure_ascii=False)
                    
            print(f"商品数据已删除: {product_id}")
            return True
            
        except Exception as e:
            print(f"删除商品数据失败 {product_id}: {str(e)}")
            return False
    
    @staticmethod
    def get_product_preview_data(result_path: str = "./result/", 
                               cache_path: str = "./cache/ids.json", 
                               limit: int = 100) -> List[Dict[str, Any]]:
        """
        获取商品预览数据
        
        Args:
            result_path: 结果文件路径
            cache_path: 缓存文件路径
            limit: 限制返回数量
            
        Returns:
            List: 商品预览数据列表
        """
        try:
            cache_dict = DataProcessor.load_all_ids(cache_path)
            preview_data = []
            
            count = 0
            for product_id, product_name in cache_dict.items():
                if count >= limit:
                    break
                    
                product_data = DataProcessor.load_product(product_id, result_path)
                if product_data:
                    info = DataProcessor.extract_product_info(product_data)
                    if info:
                        # 获取文件修改时间作为采集时间
                        result_file = os.path.join(result_path, f"{product_id}.json")
                        if os.path.exists(result_file):
                            mtime = os.path.getmtime(result_file)
                            collect_time = datetime.fromtimestamp(mtime).strftime("%Y-%m-%d %H:%M:%S")
                        else:
                            collect_time = "未知"
                            
                        preview_data.append({
                            "ID": product_id,
                            "商品名称": info.get("商品名称", product_name),
                            "价格": f"¥{info.get('价格', 0):.2f}",
                            "采集时间": collect_time
                        })
                        count += 1
                        
            return preview_data
            
        except Exception as e:
            print(f"获取预览数据失败: {str(e)}")
            return []
