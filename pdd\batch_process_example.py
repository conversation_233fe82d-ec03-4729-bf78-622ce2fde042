#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量处理商品详情页链接的示例
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.spider import Pdd<PERSON><PERSON><PERSON>


def example_batch_process_urls():
    """示例：批量处理商品链接"""
    
    def log_callback(message, level):
        print(f"[{level}] {message}")
    
    def progress_callback(progress):
        print(f"Progress: {progress}")
    
    def status_callback(status):
        print(f"Status: {status}")
    
    # 创建爬虫实例
    config = {
        "headless": True,  # 无头模式
        "delay_min": 2,
        "delay_max": 4,
        "retry_times": 3
    }
    
    spider = PddSpider(
        config=config,
        log_callback=log_callback,
        progress_callback=progress_callback,
        status_callback=status_callback
    )
    
    try:
        # 方法1：直接处理链接文件
        print("=" * 50)
        print("方法1：批量处理商品链接文件")
        print("=" * 50)
        
        # 创建示例链接文件
        sample_urls = [
            "https://mobile.pinduoduo.com/goods.html?goods_id=123456789",
            "https://mobile.pinduoduo.com/goods.html?goods_id=987654321",
            "https://mobile.pinduoduo.com/goods.html?goods_id=456789123"
        ]
        
        with open("product_urls.txt", "w", encoding="utf-8") as f:
            for url in sample_urls:
                f.write(url + "\n")
        
        print("已创建示例链接文件: product_urls.txt")
        
        # 批量处理
        result = spider.batch_process_product_urls("product_urls.txt")
        print(f"处理结果: {result}")
        
        # 方法2：从商品ID文件创建链接文件
        print("\n" + "=" * 50)
        print("方法2：从商品ID创建链接文件")
        print("=" * 50)
        
        # 创建示例ID文件
        sample_ids = ["123456789", "987654321", "456789123", "invalid_id", "111222333"]
        
        with open("product_ids.txt", "w", encoding="utf-8") as f:
            for product_id in sample_ids:
                f.write(product_id + "\n")
        
        print("已创建示例ID文件: product_ids.txt")
        
        # 从ID创建链接文件
        if spider.create_urls_file_from_ids("product_ids.txt", "generated_urls.txt"):
            print("成功创建链接文件: generated_urls.txt")
            
            # 处理生成的链接文件
            result = spider.batch_process_product_urls("generated_urls.txt")
            print(f"处理结果: {result}")
        
    except Exception as e:
        print(f"示例运行出错: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理
        spider.close()
        print("爬虫已关闭")


def example_create_urls_from_existing_cache():
    """示例：从现有缓存创建链接文件"""
    
    spider = PddSpider()
    
    try:
        # 如果有现有的缓存文件
        cache_file = "cache/ids.json"
        if os.path.exists(cache_file):
            import json
            
            with open(cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
            
            # 提取所有商品ID
            product_ids = list(cache_data.keys())
            
            # 创建ID文件
            with open("cached_product_ids.txt", "w", encoding="utf-8") as f:
                for product_id in product_ids:
                    f.write(product_id + "\n")
            
            print(f"从缓存提取了 {len(product_ids)} 个商品ID")
            
            # 创建链接文件
            if spider.create_urls_file_from_ids("cached_product_ids.txt", "cached_urls.txt"):
                print("成功创建缓存商品的链接文件")
                
                # 可以选择处理这些链接
                # result = spider.batch_process_product_urls("cached_urls.txt")
                # print(f"处理结果: {result}")
        else:
            print("未找到缓存文件")
    
    except Exception as e:
        print(f"处理缓存出错: {str(e)}")
    
    finally:
        spider.close()


if __name__ == "__main__":
    print("选择示例:")
    print("1. 批量处理商品链接")
    print("2. 从现有缓存创建链接文件")
    
    choice = input("请输入选择 (1/2): ").strip()
    
    if choice == "1":
        example_batch_process_urls()
    elif choice == "2":
        example_create_urls_from_existing_cache()
    else:
        print("无效选择")
    
    input("\n按 Enter 键退出...")
