"""
卡密系统统一接口
提供给主程序调用的统一接口
"""
import os
import functools
from typing import Dict, Any, Callable

try:
    from .license_manager import LicenseManager
    from .exceptions import LicenseError
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    from license_system.license_manager import LicenseManager
    from license_system.exceptions import LicenseError


# 检查是否启用卡密系统
LICENSE_ENABLED = os.getenv('BUILD_TYPE', 'dev') == 'release'


class LicenseInterface:
    """卡密系统统一接口"""
    
    @staticmethod
    def is_enabled() -> bool:
        """检查卡密系统是否启用"""
        return LICENSE_ENABLED
    
    @staticmethod
    def verify_startup() -> bool:
        """启动时验证卡密"""
        if not LICENSE_ENABLED:
            return True
        
        try:
            return LicenseManager.verify_startup()
        except Exception as e:
            print(f"启动验证失败: {e}")
            return False
    
    @staticmethod
    def check_feature(feature: str) -> bool:
        """检查功能权限"""
        if not LICENSE_ENABLED:
            return True
        
        try:
            return LicenseManager.check_feature(feature)
        except Exception:
            return False
    
    @staticmethod
    def get_status() -> Dict[str, Any]:
        """获取授权状态"""
        if not LICENSE_ENABLED:
            return {
                "status": "dev",
                "message": "开发模式",
                "valid": True,
                "license_type": "development",
                "type_name": "开发版本"
            }
        
        try:
            return LicenseManager.get_status()
        except Exception as e:
            return {
                "status": "error",
                "message": f"获取状态失败: {e}",
                "valid": False
            }
    
    @staticmethod
    def activate_license(license_key: str) -> bool:
        """激活卡密"""
        if not LICENSE_ENABLED:
            return True
        
        try:
            return LicenseManager.activate_license(license_key)
        except Exception as e:
            print(f"激活失败: {e}")
            return False
    
    @staticmethod
    def generate_trial() -> bool:
        """生成试用卡密"""
        if not LICENSE_ENABLED:
            return True
        
        try:
            return LicenseManager.generate_trial()
        except Exception as e:
            print(f"生成试用失败: {e}")
            return False
    
    @staticmethod
    def deactivate_license() -> bool:
        """停用卡密"""
        if not LICENSE_ENABLED:
            return True
        
        try:
            return LicenseManager.deactivate_license()
        except Exception as e:
            print(f"停用失败: {e}")
            return False


def license_required(feature: str = None, show_error: bool = True):
    """
    卡密验证装饰器
    
    Args:
        feature: 需要验证的功能名称
        show_error: 是否显示错误信息
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 如果卡密系统未启用，直接执行
            if not LICENSE_ENABLED:
                return func(*args, **kwargs)
            
            # 检查功能权限
            if feature and not LicenseInterface.check_feature(feature):
                error_msg = f"需要授权才能使用 {feature} 功能"
                if show_error:
                    print(error_msg)
                raise LicenseError(error_msg)
            
            # 执行原函数
            return func(*args, **kwargs)
        
        return wrapper
    return decorator


def get_license_decorator(feature: str = None):
    """获取卡密装饰器（兼容性方法）"""
    return license_required(feature)


# 功能权限常量
class Features:
    """功能权限常量"""
    BASIC = "basic"
    BATCH_COLLECT = "batch_collect"
    EXPORT_DATA = "export_data"
    ADVANCED_FILTER = "advanced_filter"
    AUTO_RETRY = "auto_retry"
    MULTI_THREAD = "multi_thread"
    SCHEDULE = "schedule"
    API_ACCESS = "api_access"


# 预定义的装饰器
basic_required = license_required(Features.BASIC)
batch_collect_required = license_required(Features.BATCH_COLLECT)
export_data_required = license_required(Features.EXPORT_DATA)
advanced_filter_required = license_required(Features.ADVANCED_FILTER)
auto_retry_required = license_required(Features.AUTO_RETRY)
multi_thread_required = license_required(Features.MULTI_THREAD)
schedule_required = license_required(Features.SCHEDULE)
api_access_required = license_required(Features.API_ACCESS)
