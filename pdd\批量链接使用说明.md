# 批量链接功能使用说明

## 功能概述

新增的批量链接功能允许您一次性处理多个商品详情页链接。

## 使用方法

1. 在采集模式中选择"批量链接"
2. 点击"选择文件"按钮，选择包含商品链接的文本文件
3. 文件格式要求：
   - 每行一个链接
   - 链接格式：`https://mobile.pinduoduo.com/goods.html?goods_id=商品ID`
   - 支持 `.txt` 格式文件

示例文件内容：
```
https://mobile.pinduoduo.com/goods.html?goods_id=123456789
https://mobile.pinduoduo.com/goods.html?goods_id=987654321
https://mobile.pinduoduo.com/goods.html?goods_id=456789123
```

## 功能特点

- **链接预览**：选择文件后会显示前20行内容预览
- **自动验证**：系统会验证链接格式的有效性
- **进度显示**：实时显示处理进度和结果统计
- **错误处理**：自动跳过无效链接，记录处理结果
- **停止功能**：支持随时停止批量处理

## 注意事项

1. **文件编码**：请确保文件使用 UTF-8 编码
2. **链接格式**：只支持拼多多移动端商品链接格式
3. **处理速度**：为避免被限制，系统会自动控制处理速度
4. **登录状态**：使用前请确保已登录拼多多账号
5. **文件大小**：建议单次处理不超过1000个链接

## 示例文件

程序目录下提供了示例文件：
- `sample_urls.txt`：示例链接文件

您可以参考这个文件的格式来准备自己的数据文件。

## 处理结果

批量处理完成后，系统会显示：
- 成功处理的商品数量
- 失败的商品数量
- 详细的处理日志

所有采集到的数据会保存在 `result` 目录下，按照商品ID命名的JSON文件中。
