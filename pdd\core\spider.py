"""
拼多多爬虫核心类 - 支持GUI回调
"""
import json
import os
import random
import re
import sys
import time
import threading
import socket
from typing import Callable, Optional, Dict, Any

import requests
from DrissionPage import Chromium, ChromiumOptions
from DrissionPage.common import Settings


class PddSpider:
    def __init__(self, config: Dict[str, Any] = None, 
                 log_callback: Callable[[str, str], None] = None,
                 progress_callback: Callable[[str], None] = None,
                 status_callback: Callable[[str], None] = None):
        """
        初始化拼多多爬虫
        
        Args:
            config: 配置字典
            log_callback: 日志回调函数 (message, level)
            progress_callback: 进度回调函数 (progress_text)
            status_callback: 状态回调函数 (status_text)
        """
        # 默认配置
        self.config = {
            "headless": True,
            "delay_min": 3,
            "delay_max": 5,
            "retry_times": 3,
            "result_path": "./result/",
            "cache_path": "./cache/ids.json",
            "cookies_path": "./cookies.json",
            "url_path": "./url.txt",
            "domain": "mobile.pinduoduo.com",  # 默认域名
            # 价格筛选配置
            "price_filter": {
                "enabled": True,
                "min_price": 0.0,
                "max_price": 0.5,
                "mode": "range"  # "range", "less_than", "greater_than"
            },
            # 虚拟商品筛选配置
            "virtual_goods_filter": {
                "enabled": True,
                "mode": "exclude",  # "include", "exclude", "only"
                "keywords": [
                    "充值", "会员", "虚拟", "话费", "流量", "Q币", "点券", "代充",
                    "游戏币", "钻石", "金币", "积分", "卡密", "激活码", "兑换码",
                    "网费", "电费", "水费", "燃气费", "缴费", "代缴", "充值卡",
                    "游戏充值", "手游", "端游", "网游", "在线充值", "自动发货"
                ]
            }
        }

        # 先从config.json文件加载配置
        self._load_config_from_file()

        # 再用传入的配置覆盖
        if config:
            self.config.update(config)
            
        # 回调函数
        self.log_callback = log_callback or self._default_log
        self.progress_callback = progress_callback or self._default_progress
        self.status_callback = status_callback or self._default_status
        
        # 初始化浏览器
        self.browser = None
        self.tab1 = None
        self.tab2 = None
        self.page_id = None
        self.cookies = None

        # 控制变量
        self.is_running = False
        self.should_stop = False
        self.current_phone = None
        self.last_successful_port = self._load_last_port()  # 从文件加载上次成功的端口

        self._init_browser()
        
    def _default_log(self, message: str, level: str = "INFO"):
        """默认日志输出"""
        print(f"[{level}] {message}")
        
    def _default_progress(self, progress: str):
        """默认进度输出"""
        print(f"Progress: {progress}")
        
    def _default_status(self, status: str):
        """默认状态输出"""
        print(f"Status: {status}")

    def _load_config_from_file(self):
        """从config.json文件加载配置"""
        try:
            config_file = "config.json"
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    file_config = json.load(f)
                    self.config.update(file_config)
        except Exception as e:
            self.log_callback(f"加载配置文件失败: {str(e)}", "WARNING")

    def _is_port_available(self, port: int) -> bool:
        """检查端口是否可用（空闲端口才可用于启动新浏览器）"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(1)
                # 尝试绑定端口，如果成功说明端口空闲
                sock.bind(('127.0.0.1', port))
                return True  # 绑定成功，端口可用
        except OSError:
            return False  # 绑定失败，端口被占用
        except Exception:
            return False  # 其他错误，假设端口不可用

    def _get_available_ports(self, start_port: int = 9222, count: int = 10) -> list:
        """获取可用端口列表"""
        available_ports = []
        for port in range(start_port, start_port + count * 2):  # 扩大搜索范围
            if self._is_port_available(port):
                available_ports.append(port)
                if len(available_ports) >= count:
                    break

        # 如果找不到足够的可用端口，返回默认端口列表
        if not available_ports:
            available_ports = list(range(start_port, start_port + count))
            self.log_callback("未找到可用端口，使用默认端口列表", "WARNING")

        return available_ports

    def _load_last_port(self) -> int:
        """从已加载的配置中获取上次成功的端口"""
        return self.config.get("last_successful_port")

    def _save_last_port(self, port: int):
        """保存成功的端口到config.json"""
        try:
            config_file = "config.json"
            config_data = {}

            # 读取现有配置
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)

            # 更新端口信息
            config_data["last_successful_port"] = port

            # 保存配置
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.log_callback(f"保存端口信息失败: {str(e)}", "WARNING")

    def _init_browser(self):
        """初始化浏览器"""
        # 直接使用上次成功的端口，如果没有就重新找
        if self.last_successful_port:
            available_ports = [self.last_successful_port]
        else:
            available_ports = self._get_available_ports(start_port=9222, count=8)

        for port_attempt, port in enumerate(available_ports):
            try:

                # 创建浏览器选项 - 不读取配置文件以支持打包
                co = ChromiumOptions(read_file=False)

                # 设置端口
                co.set_local_port(port)

                # 设置连接重试参数
                co.set_retry(times=2, interval=1.0)

                # 获取程序运行目录（支持打包后的exe）
                if getattr(sys, 'frozen', False):
                    # 打包后的exe环境
                    app_dir = os.path.dirname(sys.executable)
                    self.log_callback("检测到打包环境", "INFO")
                else:
                    # 开发环境
                    app_dir = os.path.dirname(os.path.abspath(__file__))
                    app_dir = os.path.dirname(app_dir)  # 回到项目根目录

                # 优先使用系统Chrome以避免并行配置错误
                system_chrome_paths = [
                    r'C:\Program Files\Google\Chrome\Application\chrome.exe',
                    r'C:\Program Files (x86)\Google\Chrome\Application\chrome.exe',
                ]

                # 添加用户目录下的Chrome路径
                username = os.getenv('USERNAME', '')
                if username:
                    system_chrome_paths.append(
                        rf'C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe'
                    )

                # 添加Edge作为备选（Edge也基于Chromium）
                system_chrome_paths.extend([
                    r'C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe',
                    r'C:\Program Files\Microsoft\Edge\Application\msedge.exe',
                ])

                chrome_found = False
                for chrome_path in system_chrome_paths:
                    if os.path.exists(chrome_path):
                        co.set_browser_path(chrome_path)
                        chrome_found = True
                        browser_name = "Edge" if "msedge.exe" in chrome_path else "Chrome"
                        self.log_callback(f"找到系统{browser_name}: {chrome_path}", "INFO")
                        break

                # 如果系统浏览器都没找到，再尝试本地Chrome
                if not chrome_found:
                    local_chrome_path = os.path.join(app_dir, 'Chrome', 'chrome.exe')
                    if os.path.exists(local_chrome_path):
                        self.log_callback(f"使用本地Chrome: {local_chrome_path}", "INFO")
                        user_data_path = os.path.join(app_dir, 'Chrome', 'userData')
                        co.set_paths(
                            browser_path=local_chrome_path,
                            user_data_path=user_data_path
                        )
                        # 确保用户数据目录存在
                        os.makedirs(user_data_path, exist_ok=True)
                        chrome_found = True

                if not chrome_found:
                    self.log_callback("未找到任何浏览器，将使用默认配置", "WARNING")

                # 添加浏览器选项以提高稳定性和兼容性
                co.set_argument('--no-sandbox')
                co.set_argument('--disable-dev-shm-usage')
                co.set_argument('--disable-gpu')
                co.set_argument('--disable-web-security')
                co.set_argument('--disable-blink-features=AutomationControlled')
                co.set_argument('--disable-extensions')
                co.set_argument('--disable-plugins')
                co.set_argument('--disable-images')  # 禁用图片加载提高速度
                co.set_argument('--disable-javascript')  # 可选：禁用JS
                co.set_argument('--disable-default-apps')
                co.set_argument('--disable-sync')
                co.set_argument('--no-first-run')
                co.set_argument('--no-default-browser-check')
                co.set_argument('--disable-background-timer-throttling')
                co.set_argument('--disable-renderer-backgrounding')
                co.set_argument('--disable-backgrounding-occluded-windows')

                # 设置用户代理
                co.set_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

                # 设置窗口大小
                co.set_argument('--window-size=1920,1080')

                # 创建浏览器实例 - 注意session_options=False以支持打包
                self.log_callback(f"正在创建浏览器实例 (端口: {port})...", "INFO")
                self.browser = Chromium(addr_or_opts=co, session_options=False)

                Settings.set_singleton_tab_obj(False)
                self.tab1 = self.browser.get_tab(1)
                self.tab2 = self.browser.get_tab(1)


                # 设置无头模式
                if self.config.get("headless", True):
                    self.tab1.set.window.hide()
                    self.log_callback("启用无头模式", "INFO")

                # 初始状态根据配置决定
                self.browser_visible = not self.config.get("headless", True)
                if self.browser_visible:
                    self.tab1.set.window.show()
                else:
                    self.tab1.set.window.hide()

                # 初始化页面URL跟踪
                self.current_page_url = None

                # 记录并保存成功的端口
                self.last_successful_port = port
                self._save_last_port(port)
                self.log_callback(f"浏览器初始化成功 (端口: {port})", "SUCCESS")
                return  # 成功则退出循环

            except Exception as e:
                # 清理失败的浏览器实例
                try:
                    if hasattr(self, 'browser') and self.browser:
                        self.browser.quit()
                except:
                    pass

                self.browser = None
                self.tab1 = None
                self.tab2 = None

                # 如果是上次成功的端口失败了，重新找端口
                if self.last_successful_port and port == self.last_successful_port:
                    available_ports = self._get_available_ports(start_port=9222, count=8)
                    continue

                # 如果不是最后一个端口，继续尝试下一个
                if port_attempt < len(available_ports) - 1:
                    time.sleep(1)
                    continue
                else:
                    break

    def _reinit_browser_if_needed(self) -> bool:
        """如果浏览器连接失败，重新初始化浏览器"""
        try:
            # 检查浏览器是否还活着
            if self.browser and self.tab1:
                try:
                    # 尝试获取当前URL来测试连接
                    _ = self.tab1.url
                    return True  # 连接正常
                except Exception:
                    self.log_callback("检测到浏览器连接断开，正在重新初始化...", "WARNING")

            # 清理旧的浏览器实例
            if self.browser:
                try:
                    self.browser.quit()
                except:
                    pass

            # 重新初始化
            self._init_browser()

            # 检查初始化是否成功
            if self.browser and self.tab1:
                self.log_callback("浏览器重新初始化成功", "SUCCESS")
                return True
            else:
                self.log_callback("浏览器重新初始化失败", "ERROR")
                return False

        except Exception as e:
            self.log_callback(f"重新初始化浏览器时出错: {str(e)}", "ERROR")
            return False

    def check_login_status(self) -> bool:
        """检查登录状态"""
        try:
            # 检查cookies文件是否存在
            if not os.path.exists(self.config["cookies_path"]):
                self.log_callback("未找到登录信息", "WARNING")
                return False

                
            # 加载cookies
            with open(self.config["cookies_path"], "r", encoding="utf-8") as f:
                self.cookies = json.load(f)
                

            self.log_callback("登录状态检查通过", "SUCCESS")
            return True
            
        except Exception as e:
            self.log_callback(f"登录状态检查失败: {str(e)}", "ERROR")
            return False

    def check_need_relogin(self) -> bool:
        """
        检查是否需要重新登录（轻量级检查）

        Returns:
            bool: 是否需要重新登录
        """
        try:
            if not self.tab1:
                return False

            # 检查当前页面是否被重定向到登录页
            current_url = self.tab1.url
            if 'login' in current_url.lower():
                self.log_callback("检测到需要重新登录，自动停止采集", "WARNING")
                self.should_stop = True
                return True

            return False

        except Exception:
            # 出错时不停止采集
            return False

    def _is_price_valid(self, price: float) -> bool:
        """
        检查价格是否符合筛选条件

        Args:
            price: 商品价格

        Returns:
            bool: 是否符合条件
        """
        try:
            if not self.config["price_filter"]["enabled"]:
                return True

            mode = self.config["price_filter"]["mode"]
            min_price = self.config["price_filter"]["min_price"]
            max_price = self.config["price_filter"]["max_price"]

            if mode == "range":
                return min_price <= price <= max_price
            elif mode == "less_than":
                return price < max_price
            elif mode == "greater_than":
                return price > min_price
            else:
                return True

        except Exception as e:
            self.log_callback(f"价格筛选检查失败: {str(e)}", "WARNING")
            return True

    def _is_virtual_goods(self, goods_name: str) -> bool:
        """
        检查是否为虚拟商品

        Args:
            goods_name: 商品名称

        Returns:
            bool: 是否为虚拟商品
        """
        try:
            if not self.config["virtual_goods_filter"]["enabled"]:
                return False

            keywords = self.config["virtual_goods_filter"]["keywords"]
            goods_name_lower = goods_name.lower()

            for keyword in keywords:
                if keyword.lower() in goods_name_lower:
                    return True

            return False

        except Exception as e:
            self.log_callback(f"虚拟商品检查失败: {str(e)}", "WARNING")
            return False

    def _should_collect_goods(self, goods_name: str, price: float) -> bool:
        """
        综合判断是否应该采集该商品

        Args:
            goods_name: 商品名称
            price: 商品价格

        Returns:
            bool: 是否应该采集
        """
        try:
            # 价格筛选
            if not self._is_price_valid(price):
                self.log_callback(f"价格不符合条件，跳过: {goods_name} (¥{price})", "INFO")
                return False

            # 虚拟商品筛选
            is_virtual = self._is_virtual_goods(goods_name)
            virtual_mode = self.config["virtual_goods_filter"]["mode"]

            if self.config["virtual_goods_filter"]["enabled"]:
                if virtual_mode == "exclude" and is_virtual:
                    self.log_callback(f"虚拟商品已排除: {goods_name}", "INFO")
                    return False
                elif virtual_mode == "only" and not is_virtual:
                    self.log_callback(f"非虚拟商品已排除: {goods_name}", "INFO")
                    return False
                elif virtual_mode == "include":
                    # include模式表示包含所有商品，不做额外筛选
                    pass

            return True

        except Exception as e:
            self.log_callback(f"商品筛选检查失败: {str(e)}", "WARNING")
            return True
            
    def start_login_process(self, phone_number: str, domain: str = "mobile.pinduoduo.com") -> bool:
        """
        开始登录流程 - 第一步：输入手机号并发送验证码

        Args:
            phone_number: 手机号码
            domain: 登录域名，默认为 mobile.pinduoduo.com

        Returns:
            bool: 是否成功发送验证码
        """
        try:
            # 保存选择的域名到配置
            self.config["domain"] = domain

            self.status_callback("正在发送验证码...")
            self.log_callback(f"开始为手机号 {phone_number} 发送验证码 (域名: {domain})", "INFO")

            # 检查浏览器是否初始化成功
            if not self.browser or not self.tab1:
                self.log_callback("浏览器未初始化，正在重新初始化...", "WARNING")
                if not self._reinit_browser_if_needed():
                    self.log_callback("浏览器初始化失败，无法登录", "ERROR")
                    return False

            # 访问登录页面
            self.log_callback(f"正在访问登录页面 ({domain})...", "INFO")
            login_url = f'https://{domain}/login.html'
            self.tab1.get(login_url)
            self.current_page_url = login_url  # 记录当前页面URL

            # 点击登录按钮
            login_button = self.tab1.ele('x://*[@id="first"]/div[2]/div', timeout=10)
            if login_button:
                login_button.click()
                self.log_callback("已点击登录按钮", "INFO")

            # 确认协议
            confirm = self.tab1.ele('x://*[@id="container"]/form/div[2]/p/i', timeout=5)
            if confirm:
                confirm.click()
                self.log_callback("已确认用户协议", "INFO")

            # 清空并输入手机号
            number_input = self.tab1.ele('@id=user-mobile', timeout=10)
            if number_input:
                number_input.clear()
                number_input.input(phone_number)
                self.log_callback(f"已输入手机号: {phone_number}", "INFO")
            else:
                self.log_callback("未找到手机号输入框", "ERROR")
                return False

            # 点击获取验证码
            send_code_button = self.tab1.ele('@id=code-button', timeout=10)
            if send_code_button:
                send_code_button.click()
                self.log_callback("已点击获取验证码按钮", "INFO")

                # 检查是否出现滑块验证
                time.sleep(2)  # 等待页面响应
                slider_element = self.tab1.ele('@class=geetest_slider_button', timeout=3)
                if slider_element:
                    self.log_callback("检测到滑块验证，请手动完成", "WARNING")
                    # 显示浏览器让用户手动处理
                    self.show_browser_for_manual_verify()
                    self.log_callback("请在浏览器中完成滑块验证，然后继续", "INFO")

            else:
                self.log_callback("未找到获取验证码按钮", "ERROR")
                return False

            # 处理语音验证码弹窗（如果有）
            popup = self.tab1.ele('@class=_3E7ieRE-', timeout=3)
            if popup:
                self.log_callback("检测到语音验证码选项，已点击", "INFO")
                popup.click()

            # 保存当前状态
            self.current_phone = phone_number

            self.log_callback("验证码发送成功", "SUCCESS")
            return True

        except Exception as e:
            self.log_callback(f"发送验证码失败: {str(e)}", "ERROR")
            return False

    def complete_login_process(self, verification_code: str) -> bool:
        """
        完成登录流程 - 第二步：输入验证码并完成登录

        Args:
            verification_code: 验证码

        Returns:
            bool: 是否登录成功
        """
        try:
            self.status_callback("正在验证登录...")
            self.log_callback(f"开始验证验证码", "INFO")

            # 输入验证码
            code_input = self.tab1.ele('@id=input-code', timeout=10)
            if code_input:
                code_input.clear()
                code_input.input(verification_code)
                self.log_callback("已输入验证码", "INFO")
            else:
                self.log_callback("未找到验证码输入框", "ERROR")
                return False

            # 开始监听登录请求
            self.tab1.listen.start('/v3?pdduid')

            # 点击登录按钮
            login_btn = self.tab1.ele('@id=submit-button', timeout=10)
            if login_btn:
                login_btn.click()
                self.log_callback("已点击登录按钮", "INFO")
            else:
                self.log_callback("未找到登录按钮", "ERROR")
                return False

            # 检查登录结果
            content = self.tab1.ele('@class=KNVRSU85', timeout=15)
            if content:
                self.log_callback("登录成功！", "SUCCESS")

                self.tab1.refresh()

                # 获取cookies
                pre_cookies = self.tab1.cookies()
                cookies = {x['name']: x['value'] for x in pre_cookies}

                # 保存cookies
                os.makedirs(os.path.dirname(self.config["cookies_path"]), exist_ok=True)
                with open(self.config["cookies_path"], 'w', encoding='utf-8') as f:
                    json.dump(cookies, f, indent=2, ensure_ascii=False)

                self.cookies = cookies

                self.log_callback("登录信息已保存", "SUCCESS")
                self.status_callback("登录完成")

                return True
            else:
                self.log_callback("登录失败，请检查验证码是否正确", "ERROR")
                return False

        except Exception as e:
            self.log_callback(f"登录验证失败: {str(e)}", "ERROR")
            return False

    def login(self, phone_callback: Callable[[], str] = None,
              code_callback: Callable[[], str] = None) -> bool:
        """
        登录拼多多账号
        
        Args:
            phone_callback: 获取手机号的回调函数
            code_callback: 获取验证码的回调函数
            
        Returns:
            bool: 登录是否成功
        """
        try:
            self.status_callback("正在登录...")
            self.log_callback("开始登录流程", "INFO")

            # 检查浏览器是否初始化成功
            if not self.browser or not self.tab1:
                self.log_callback("浏览器未初始化，正在重新初始化...", "WARNING")
                if not self._reinit_browser_if_needed():
                    self.log_callback("浏览器初始化失败，无法登录", "ERROR")
                    return False

            # 如果没有提供回调函数，使用控制台输入
            if not phone_callback:
                phone_callback = lambda: input("请输入手机号: ")
            if not code_callback:
                code_callback = lambda: input("请输入验证码: ")

            while True:
                domain = self.config.get("domain", "mobile.pinduoduo.com")
                login_url = f'https://{domain}/login.html'
                self.log_callback(f"正在访问登录页面 ({domain})...", "INFO")
                self.tab1.get(login_url)
                
                # 点击登录按钮
                login_button = self.tab1.ele('x://*[@id="first"]/div[2]/div')
                if login_button:
                    login_button.click()
                    
                # 确认协议
                confirm = self.tab1.ele('x://*[@id="container"]/form/div[2]/p/i')
                if confirm:
                    confirm.click()
                    
                # 清空手机号输入框
                number_input = self.tab1.ele('@id=user-mobile')
                if number_input:
                    number_input.clear()
                    
                # 获取手机号
                while True:
                    try:
                        phone = phone_callback()
                        if len(phone) == 11 and phone.isdigit():
                            break
                        else:
                            self.log_callback("手机号格式错误，请重新输入", "ERROR")
                    except Exception as e:
                        self.log_callback(f"获取手机号失败: {str(e)}", "ERROR")
                        return False
                        
                # 输入手机号
                number_input.input(phone)
                self.log_callback(f"已输入手机号: {phone}", "INFO")
                
                # 点击获取验证码
                send_code_button = self.tab1.ele('@id=code-button')
                if send_code_button:
                    send_code_button.click()
                    self.log_callback("已发送验证码", "INFO")
                    
                # 处理语音验证码弹窗
                popup = self.tab1.ele('@class=_3E7ieRE-', timeout=2)
                if popup:
                    self.log_callback("检测到语音验证码选项", "INFO")
                    popup.click()
                    
                # 获取验证码
                while True:
                    try:
                        code = code_callback()
                        if code and len(code) == 6 and code.isdigit():
                            break
                        elif not code:
                            # 重新获取验证码
                            self.tab1.refresh()
                            time.sleep(1)
                            continue
                        else:
                            self.log_callback("验证码格式错误，请重新输入", "ERROR")
                    except Exception as e:
                        self.log_callback(f"获取验证码失败: {str(e)}", "ERROR")
                        return False
                        
                # 输入验证码
                code_input = self.tab1.ele('@id=input-code')
                if code_input:
                    code_input.input(code)
                    self.log_callback("已输入验证码", "INFO")
                    
                # 开始监听登录请求
                self.tab1.listen.start('/v3?pdduid')
                
                # 点击登录按钮
                login_btn = self.tab1.ele('@id=submit-button')
                if login_btn:
                    login_btn.click()
                    
                # 检查登录结果
                content = self.tab1.ele('@class=KNVRSU85', timeout=10)
                if content:
                    self.log_callback("登录成功！", "SUCCESS")
                    break
                else:
                    self.log_callback("登录失败，请重试", "ERROR")
                    
            # 保存登录信息
            time.sleep(random.randint(1, 3))
            self.tab1.refresh()
            
            # 获取cookies
            pre_cookies = self.tab1.cookies()
            cookies = {x['name']: x['value'] for x in pre_cookies}
            
            # 保存cookies
            os.makedirs(os.path.dirname(self.config["cookies_path"]), exist_ok=True)
            with open(self.config["cookies_path"], 'w', encoding='utf-8') as f:
                json.dump(cookies, f, indent=2, ensure_ascii=False)
                
            self.cookies = cookies

            self.log_callback("登录信息已保存", "SUCCESS")
            self.status_callback("登录完成")
            
            return True
            
        except Exception as e:
            self.log_callback(f"登录过程出错: {str(e)}", "ERROR")
            self.status_callback("登录失败")
            return False

    def clean_detail_data(self, product_id: str, text: str, name: str=None) -> bool:
        """
        清理和保存商品详情数据

        Args:
            product_id: 商品ID
            name: 商品名称
            text: 页面HTML内容

        Returns:
            bool: 是否成功保存
        """
        try:
            result_path = os.path.join(self.config["result_path"], f'{product_id}.json')
            cache_path = self.config["cache_path"]

            # 提取数据
            pattern = r"window\.rawData\s*=\s*(.*?)\s*<\/"
            match = re.search(pattern, text)



            if not match:
                self.log_callback(f"未找到商品数据: {product_id}", "WARNING")
                return False

            data_str = match.group(1)
            data_dict = json.loads(data_str.rstrip(';'))

            # 检查数据有效性
            if 'store' not in data_dict or 'initDataObj' not in data_dict['store']:
                self.log_callback(f"数据格式异常: {product_id}", "WARNING")
                return False


            if 'goods' not in data_dict['store']['initDataObj']:
                self.log_callback("需要重新登录，立即停止采集", "WARNING")
                return 'relogin'

            goods_data = data_dict['store']['initDataObj']['goods']
            if goods_data.get('statusExplain') == "商品已售罄，推荐以下相似商品":
                self.log_callback("账号被风控", "WARNING")
                return 'forbid'

            # 保存结果文件
            os.makedirs(os.path.dirname(result_path), exist_ok=True)
            with open(result_path, 'w', encoding='utf-8') as f:
                json.dump(data_dict, f, indent=2, ensure_ascii=False)

            self.log_callback(f"商品 {product_id} 采集成功", "SUCCESS")

            # 更新缓存
            os.makedirs(os.path.dirname(cache_path), exist_ok=True)
            try:
                with open(cache_path, 'r', encoding='utf-8') as f:
                    cache_dict = json.load(f)
            except:
                cache_dict = {}

            cache_dict[product_id] = name
            with open(cache_path, 'w', encoding='utf-8') as f:
                json.dump(cache_dict, f, indent=2, ensure_ascii=False)

            return True

        except Exception as e:
            self.log_callback(f"保存商品数据失败 {product_id}: {str(e)}", "ERROR")
            return False

    def _get_delay(self, delay_type: str = "normal") -> int:
        """
        获取配置的延时时间

        Args:
            delay_type: 延时类型 ("normal", "short", "long")

        Returns:
            int: 随机延时秒数
        """
        delay_min = self.config.get("delay_min", 3)
        delay_max = self.config.get("delay_max", 5)

        if delay_type == "short":
            # 短延时：配置时间的一半
            return random.randint(max(1, delay_min // 2), max(2, delay_max // 2))
        elif delay_type == "long":
            # 长延时：配置时间的1.5倍
            return random.randint(delay_min, int(delay_max * 1.5))
        else:
            # 正常延时：使用配置的时间
            return random.randint(delay_min, delay_max)

    def _interruptible_sleep(self, seconds: float) -> bool:
        """
        可中断的睡眠函数

        Args:
            seconds: 睡眠时间（秒）

        Returns:
            bool: True表示正常完成，False表示被中断
        """
        import time

        # 将睡眠时间分成小段，每0.1秒检查一次停止标志
        check_interval = 0.1
        total_time = 0

        while total_time < seconds:
            if self.should_stop:
                return False

            sleep_time = min(check_interval, seconds - total_time)
            time.sleep(sleep_time)
            total_time += sleep_time

        return True

    def fetch_single_product(self, product_url: str = "") -> bool:
        """
        采集商品详情链接

        Args:
            product_url: 商品URL（可选）

        Returns:
            bool: 是否采集成功
        """
        try:
            product_id = product_url.split('&')[0].split('=')[1]

            self.log_callback(f"开始采集商品: {product_id}", "INFO")
            # 访问商品页面
            self.tab2.listen.start('goods.html?goods_id')
            self.tab1.get(product_url)

            Response = self.tab2.listen.wait()

            # 处理数据
            # 处理商品数据
            result = self.clean_detail_data(product_id=product_id, text=Response.response.body)

            # 检查是否需要重新登录
            if result == 'relogin':
                self.log_callback(f"需要重新登录，停止采集商品: {product_id}", "WARNING")
                return False

            if result == 'forbid':
                return False

            # 处理滑块验证
            slider_attempts = 0
            while not result and slider_attempts < 3:
                slider_attempts += 1
                self.log_callback(f"检测到滑块验证，第{slider_attempts}次尝试: {product_id}", "WARNING")

                # 显示浏览器让用户处理滑块
                if not self.browser_visible:
                    self.tab1.set.window.show()
                    self.browser_visible = True
                    self.log_callback("请手动完成滑块验证", "INFO")

                self.tab2.listen.start('goods.html?goods_id')
                Response = self.tab2.listen.wait()

                if Response:
                    result = self.clean_detail_data(product_id=product_id, text=Response.response.body)

                    if result == 'relogin':
                        self.log_callback(f"需要重新登录，停止采集商品: {product_id}", "WARNING")
                        return False

                    elif result:
                        self.log_callback("滑块验证通过", "SUCCESS")
                        # 验证成功后隐藏浏览器
                        if self.config.get("headless", True):
                            self.tab1.set.window.hide()
                            self.browser_visible = False


            if result:
                self.status_callback(f"商品 {product_id} 采集完成")
                return True
            else:
                self.status_callback(f"商品 {product_id} 采集失败")
                return False

        except Exception as e:
            self.log_callback(f"采集商品失败 {product_id}: {str(e)}", "ERROR")
            self.status_callback(f"商品 {product_id} 采集失败")
            return False

    def clean_first_page(self, text: str, goods_type: str = None) -> Dict[str, Dict[str, any]]:
        """
        清理首页的商品数据

        Args:
            text: 页面HTML内容
            goods_type: 商品类型 ('食品', '百货', '女装', '男装')

        Returns:
            Dict[str, Dict]: 商品ID到商品信息的映射 {goods_id: {"name": name, "price": price}}
        """
        try:
            pattern = r"window\.rawData\s*=\s*(.*?)\s*;"
            match = re.search(pattern, text)
            goods_dict = {}

            if match:
                data_str = match.group(1)
                data_dict = json.loads(data_str.rstrip(';'))

                # 根据商品类型选择对应的tabDataMap键
                tab_id_map = {
                    "食品": "1",
                    "百货": "15",
                    "女装": "14",
                    "男装": "743"
                }

                if goods_type in tab_id_map:
                    tab_id = tab_id_map.get(goods_type)
                    goods_info = data_dict.get("stores", {}).get("store", {}).get("tabDataMap", {}).get(tab_id, {}).get("catGoodsList", {}).get("list", [])
                    for good in goods_info:
                        goods_id = good.get('goods_id')
                        goods_name = good.get('goods_name')
                        goods_price = good.get('group', {}).get('price_info', 0)
                        if goods_id and goods_name:
                            try:
                                price_float = float(goods_price) if goods_price else 0.0
                                goods_dict[str(goods_id)] = {
                                    "name": goods_name,
                                    "price": price_float
                                }
                            except (ValueError, TypeError):
                                self.log_callback(f"价格解析失败: {goods_name} - {goods_price}", "WARNING")
                                goods_dict[str(goods_id)] = {
                                    "name": goods_name,
                                    "price": 0.0
                                }

                else:
                    goods_info = data_dict["stores"]["store"]["mainListProps"]["goodsList"]
                    for good in goods_info:
                        goods_id = good.get("data", {}).get('goods_id')
                        goods_name = good.get("data", {}).get('goods_name')
                        goods_price = good.get("data", {}).get("group", {}).get("price_info", 0)
                        if goods_id and goods_name:
                            try:
                                price_float = float(goods_price) if goods_price else 0.0
                                goods_dict[str(goods_id)] = {
                                    "name": goods_name,
                                    "price": price_float
                                }
                            except (ValueError, TypeError):
                                self.log_callback(f"价格解析失败: {goods_name} - {goods_price}", "WARNING")
                                goods_dict[str(goods_id)] = {
                                    "name": goods_name,
                                    "price": 0.0
                                }

                self.log_callback(f"首页解析到 {len(goods_dict)} 个{goods_type or '推荐'}商品", "INFO")
                return goods_dict

            return {}
        except Exception as e:
            self.log_callback(f"清理首页数据失败: {str(e)}", "ERROR")
            return {}

    def clean_extra_page(self, response_data: dict) -> Dict[str, Dict[str, any]]:
        """
        清理额外推荐页面的商品数据

        Args:
            response_data: 页面响应的JSON数据

        Returns:
            Dict[str, Dict]: 商品ID到商品信息的映射 {goods_id: {"name": name, "price": price}}
        """
        try:
            goods_dict = {}

            if not response_data or "data" not in response_data:
                return goods_dict

            goods_info = response_data.get("data", {}).get("goods_list", [])

            for good in goods_info:
                goods_data = good.get('data', {})
                goods_id = goods_data.get('goods_id')
                goods_name = goods_data.get('goods_name')
                goods_price = goods_data.get('group', {}).get('price_info', 0)

                if goods_id and goods_name:
                    try:
                        price_float = float(goods_price) if goods_price else 0.0
                        goods_dict[str(goods_id)] = {
                            "name": goods_name,
                            "price": price_float
                        }
                    except (ValueError, TypeError):
                        self.log_callback(f"价格解析失败: {goods_name} - {goods_price}", "WARNING")
                        goods_dict[str(goods_id)] = {
                            "name": goods_name,
                            "price": 0.0
                        }

            self.log_callback(f"额外页面解析到 {len(goods_dict)} 个商品", "INFO")
            return goods_dict

        except Exception as e:
            self.log_callback(f"清理额外页面数据失败: {str(e)}", "ERROR")
            return {}
    def _collect_single_product_from_page(self, product_id: str, product_name: str) -> bool:
        """
        从当前页面采集单个商品

        Args:
            product_id: 商品ID
            product_name: 商品名称

        Returns:
            bool: 是否采集成功
        """
        try:
            # 检查停止标志
            if self.should_stop:
                self.log_callback("用户停止采集，跳过商品", "INFO")
                return False

            self.log_callback(f"正在采集商品: {product_id}:{product_name}", "INFO")

            # 查找商品按钮
            good_button = self.tab1.ele(f'@text()={product_name}', timeout=5)
            if not good_button:
                self.log_callback(f"未找到商品按钮: {product_name}", "WARNING")
                return 'unfound'

            # 再次检查停止标志
            if self.should_stop:
                self.log_callback("用户停止采集，跳过商品", "INFO")
                return False

            # 开始监听商品详情请求
            self.tab2.listen.start('goods.html?goods_id')

            # 随机延时后点击
            if not self._interruptible_sleep(self._get_delay("normal")):
                self.log_callback("用户停止采集，中断延时", "INFO")
                return False
            good_button.click()

            # 等待响应
            Response = self.tab2.listen.wait(timeout=5)

            # 如果没有响应，尝试重新获取
            retry_count = 0
            while not Response and retry_count < 3:
                # 检查停止标志
                if self.should_stop:
                    self.log_callback("用户停止采集，中断重试", "INFO")
                    return False

                if not self._interruptible_sleep(self._get_delay('normal')):
                    self.log_callback("用户停止采集，中断重试延时", "INFO")
                    return False
                self.tab1.scroll.to_bottom()

                good_button = self.tab1.ele(f'@text()={product_name}', timeout=5)
                if not good_button:
                    self.tab1.back()
                    good_button = self.tab1.ele(f'@text()={product_name}', timeout=5)

                if good_button:
                    self.tab2.listen.start('goods.html?goods_id')
                    good_button.click()
                    Response = self.tab2.listen.wait(timeout=5)

                retry_count += 1

            if not Response:
                self.log_callback(f"商品详情加载失败: {product_name}", "ERROR")
                return False



            # 处理商品数据
            result = self.clean_detail_data(product_id=product_id, name=product_name, text=Response.response.body)

            # 检查是否需要重新登录
            if result == 'relogin':
                self.log_callback(f"需要重新登录，停止采集商品: {product_name}", "WARNING")
                return False

            if result == 'forbid':
                return False

            # 处理滑块验证
            slider_attempts = 0
            while not result and slider_attempts < 3:
                # 检查停止标志
                if self.should_stop:
                    self.log_callback("用户停止采集，中断滑块验证", "INFO")
                    return False

                slider_attempts += 1
                self.log_callback(f"检测到滑块验证，第{slider_attempts}次尝试: {product_name}", "WARNING")

                # 显示浏览器让用户处理滑块
                if not self.browser_visible:
                    self.tab1.set.window.show()
                    self.browser_visible = True
                    self.log_callback("请手动完成滑块验证", "INFO")

                self.tab2.listen.start('goods.html?goods_id')
                response = self.tab2.listen.wait()

                if response:
                    result = self.clean_detail_data(product_id=product_id, name=product_name, text=response.response.body)
                    if result == 'relogin':
                        self.log_callback(f"需要重新登录，停止采集商品: {product_name}", "WARNING")
                        return False
                    elif result:
                        self.log_callback("滑块验证通过", "SUCCESS")
                        # 验证成功后隐藏浏览器
                        if self.config.get("headless", True):
                            self.tab1.set.window.hide()
                            self.browser_visible = False

            # 最终结果判断
            if result is True:
                self.log_callback(f"商品采集成功: {product_name}", "SUCCESS")
                self.tab1.back()
                return True

            else:
                self.log_callback(f"商品采集失败: {product_name}", "ERROR")
                self.tab1.back()
                return False

        except Exception as e:
            self.log_callback(f"采集商品异常 {product_id}: {str(e)}", "ERROR")
            try:
                self.tab1.back()
            except:
                pass
            return False

    def batch_collect(self, pages: int = 1) -> Dict[str, int]:
        """
        批量采集商品 - 仿照auto.py的抓取逻辑

        Args:
            pages: 采集页数

        Returns:
            Dict: 采集结果统计 {"success": 成功数量, "failed": 失败数量}
        """
        try:
            # 重置停止标志
            self.should_stop = False

            self.log_callback(f"开始批量采集，计划采集 {pages} 页", "INFO")
            self.log_callback(f"筛选配置: {self.get_filter_statistics()}", "INFO")

            success_count = 0
            failed_count = 0

            # 检查浏览器是否初始化
            if not self.browser or not self.tab1:
                self.log_callback("浏览器未初始化，正在重新初始化...", "WARNING")
                if not self._reinit_browser_if_needed():
                    self.log_callback("浏览器初始化失败，无法采集", "ERROR")
                    return {"success": 0, "failed": 1}

            # 加载已采集的商品缓存
            try:
                with open(self.config["cache_path"], 'r', encoding='utf-8') as f:
                    pre_dict = json.load(f)
            except:
                pre_dict = {}


            # 获取当前配置的域名
            domain = self.config.get("domain", "mobile.pinduoduo.com")
            home_url = f'https://{domain}/'

            # 开始监听首页请求
            self.tab2.listen.start(home_url)
            self.tab1.get(home_url)
            self.log_callback(f"访问推荐页页面 ({domain})", "INFO")

            # 等待首页加载完成
            response = self.tab2.listen.wait(timeout=10)
            if not response:
                self.log_callback("需要重新登录.....", "ERROR")
                return {"success": 0, "failed": 1}

            # 解析首页商品数据
            goods_dict = self.clean_first_page(response.response.body)

            # 获取页面上的商品名称
            goods_name = self.tab1.s_eles('@class=J9WPy2Wu', timeout=2)
            if goods_name:
                goods_name = goods_name.get.texts()
            else:
                self.log_callback("未找到商品名称元素", "WARNING")

            # 筛选出页面上显示的商品ID，并应用价格和虚拟商品筛选
            for goods_id, goods_info in goods_dict.items():
                # 检查停止标志
                if self.should_stop:
                    self.log_callback("用户停止采集", "INFO")
                    return {"success": success_count, "failed": failed_count}

                if goods_info["name"] in goods_name:
                    if self._should_collect_goods(goods_info["name"], goods_info["price"]):
                        result = self._collect_single_product_from_page(product_id=goods_id, product_name=goods_info["name"])
                        if not result:
                            return {"success": success_count, "failed": failed_count}
                        elif result == 'unfound':
                            self.log_callback(f"跳过: {goods_info['name']} (¥{goods_info['price']})", "INFO")
                        else:
                            success_count += 1

                    else:
                        self.log_callback(f"筛选跳过: {goods_info['name']} (¥{goods_info['price']})", "INFO")

                self._get_delay('normal')

            page = 2
            pre_goods = []  # 本次采集已处理的商品名称

            while page <= pages:
                if self.should_stop:
                    self.log_callback("用户停止采集", "INFO")
                    break

                self.log_callback(f"正在处理第 {page} 页", "INFO")
                self.status_callback(f"正在处理第 {page} 页...")
                goods_dict = {}

                # 监听分页请求
                if not self._interruptible_sleep(self._get_delay("normal")):
                    self.log_callback("用户停止采集，中断分页延时", "INFO")
                    break
                self.tab2.listen.start('/v3?pdduid=')
                self.tab1.scroll.to_bottom()

                response = self.tab2.listen.wait(timeout=10)
                if response:
                    try:
                        page_data = response.response.body

                        extra_goods = self.clean_extra_page(page_data)
                        self.log_callback(f"第{page}页解析到 {len(extra_goods)} 个商品", "INFO")
                        goods_dict.update(extra_goods)

                        goods_name = self.tab1.s_eles('@class=J9WPy2Wu', timeout=2)
                        if goods_name:
                            goods_name = goods_name.get.texts()
                        else:
                            self.log_callback("未找到商品名称元素", "WARNING")
                            return {"success": 0, "failed": 1}

                        for goods_id, goods_info in goods_dict.items():
                            # 检查停止标志
                            if self.should_stop:
                                self.log_callback("用户停止采集", "INFO")
                                return {"success": success_count, "failed": failed_count}

                            if goods_info["name"] in goods_name:
                                if self._should_collect_goods(goods_info["name"], goods_info["price"]):
                                    result = self._collect_single_product_from_page(product_id=goods_id,product_name=goods_info["name"])
                                    if not result:
                                        return {"success": success_count, "failed": failed_count}
                                    elif result != 'unfound':
                                        success_count += 1
                                else:
                                    self.log_callback(f"筛选跳过: {goods_info['name']} (¥{goods_info['price']})",
                                                      "INFO")

                            self._get_delay('normal')

                        page += 1

                    except Exception as e:
                        self.log_callback(f"第 {page} 页处理出错: {str(e)}", "ERROR")
                        failed_count += 1
                        page += 1

                self._get_delay('normal')


            result = {"success": success_count, "failed": failed_count}
            self.log_callback(f"批量采集完成，成功: {success_count}, 失败: {failed_count}", "SUCCESS")
            self.status_callback("批量采集完成")

            return result

        except Exception as e:
            self.log_callback(f"批量采集出错: {str(e)}", "ERROR")
            self.status_callback("批量采集失败")
            return {"success": 0, "failed": 1}

    def stop_collect(self):
        """停止采集"""
        self.should_stop = True
        self.log_callback("正在停止采集...", "INFO")

        # 尝试停止浏览器的监听和等待
        try:
            if hasattr(self, 'tab2') and self.tab2:
                self.tab2.listen.stop()
        except:
            pass

        # 设置状态为停止
        self.status_callback("采集已停止")

    def update_price_filter(self, enabled: bool = None, min_price: float = None,
                           max_price: float = None, mode: str = None):
        """
        更新价格筛选配置

        Args:
            enabled: 是否启用价格筛选
            min_price: 最低价格
            max_price: 最高价格
            mode: 筛选模式 ("range", "less_than", "greater_than")
        """
        try:
            if enabled is not None:
                self.config["price_filter"]["enabled"] = enabled
            if min_price is not None:
                self.config["price_filter"]["min_price"] = min_price
            if max_price is not None:
                self.config["price_filter"]["max_price"] = max_price
            if mode is not None:
                self.config["price_filter"]["mode"] = mode

            self.log_callback(f"价格筛选配置已更新", "INFO")

        except Exception as e:
            self.log_callback(f"更新价格筛选配置失败: {str(e)}", "ERROR")

    def update_virtual_goods_filter(self, enabled: bool = None, mode: str = None,
                                   keywords: list = None):
        """
        更新虚拟商品筛选配置

        Args:
            enabled: 是否启用虚拟商品筛选
            mode: 筛选模式 ("include", "exclude", "only")
            keywords: 虚拟商品关键词列表
        """
        try:
            if enabled is not None:
                self.config["virtual_goods_filter"]["enabled"] = enabled
            if mode is not None:
                self.config["virtual_goods_filter"]["mode"] = mode
            if keywords is not None:
                self.config["virtual_goods_filter"]["keywords"] = keywords

            self.log_callback(f"虚拟商品筛选配置已更新", "INFO")

        except Exception as e:
            self.log_callback(f"更新虚拟商品筛选配置失败: {str(e)}", "ERROR")

    def get_filter_config(self) -> Dict[str, any]:
        """
        获取当前筛选配置

        Returns:
            Dict: 当前的筛选配置
        """
        return {
            "price_filter": self.config["price_filter"].copy(),
            "virtual_goods_filter": self.config["virtual_goods_filter"].copy()
        }

    def add_virtual_goods_keyword(self, keyword: str):
        """
        添加虚拟商品关键词

        Args:
            keyword: 要添加的关键词
        """
        try:
            if keyword and keyword not in self.config["virtual_goods_filter"]["keywords"]:
                self.config["virtual_goods_filter"]["keywords"].append(keyword)
                self.log_callback(f"已添加虚拟商品关键词: {keyword}", "INFO")
            else:
                self.log_callback(f"关键词已存在或无效: {keyword}", "WARNING")

        except Exception as e:
            self.log_callback(f"添加关键词失败: {str(e)}", "ERROR")

    def remove_virtual_goods_keyword(self, keyword: str):
        """
        移除虚拟商品关键词

        Args:
            keyword: 要移除的关键词
        """
        try:
            if keyword in self.config["virtual_goods_filter"]["keywords"]:
                self.config["virtual_goods_filter"]["keywords"].remove(keyword)
                self.log_callback(f"已移除虚拟商品关键词: {keyword}", "INFO")
            else:
                self.log_callback(f"关键词不存在: {keyword}", "WARNING")

        except Exception as e:
            self.log_callback(f"移除关键词失败: {str(e)}", "ERROR")

    def get_filter_statistics(self) -> str:
        """
        获取筛选配置的统计信息

        Returns:
            str: 筛选配置的描述文本
        """
        try:
            stats = []

            # 价格筛选统计
            price_config = self.config["price_filter"]
            if price_config["enabled"]:
                mode = price_config["mode"]
                min_price = price_config["min_price"]
                max_price = price_config["max_price"]

                if mode == "range":
                    stats.append(f"价格范围: ¥{min_price} - ¥{max_price}")
                elif mode == "less_than":
                    stats.append(f"价格小于: ¥{max_price}")
                elif mode == "greater_than":
                    stats.append(f"价格大于: ¥{min_price}")
            else:
                stats.append("价格筛选: 已禁用")

            # 虚拟商品筛选统计
            virtual_config = self.config["virtual_goods_filter"]
            if virtual_config["enabled"]:
                mode = virtual_config["mode"]
                keyword_count = len(virtual_config["keywords"])

                mode_text = {
                    "include": "包含所有商品",
                    "exclude": "排除虚拟商品",
                    "only": "仅采集虚拟商品"
                }.get(mode, mode)

                stats.append(f"虚拟商品: {mode_text} (关键词数量: {keyword_count})")
            else:
                stats.append("虚拟商品筛选: 已禁用")

            return " | ".join(stats)

        except Exception as e:
            return f"获取筛选统计失败: {str(e)}"

    def toggle_browser_visibility(self):
        """切换浏览器显示/隐藏"""
        try:
            if not self.browser or not self.tab1:
                self.log_callback("浏览器未初始化", "WARNING")
                return False

            if self.browser_visible:
                # 隐藏浏览器
                self.tab1.set.window.hide()
                self.browser_visible = False
                self.log_callback("浏览器已隐藏", "INFO")
            else:
                # 显示浏览器
                self.tab1.set.window.show()
                self.browser_visible = True

                # 检查页面是否为空白，如果是则刷新
                try:
                    current_url = self.tab1.url
                    if not current_url or current_url == "about:blank" or "data:" in current_url:
                        self.log_callback("检测到空白页面，正在刷新...", "INFO")
                        # 重新访问当前应该在的页面
                        if hasattr(self, 'current_page_url') and self.current_page_url:
                            self.tab1.get(self.current_page_url)
                        else:
                            # 默认访问拼多多首页
                            domain = self.config.get("domain", "mobile.pinduoduo.com")
                            self.tab1.get(f'https://{domain}')
                        time.sleep(self._get_delay('short'))
                    else:
                        # 刷新当前页面以确保内容正确显示
                        self.tab1.refresh()
                        time.sleep(self._get_delay('short'))
                except Exception as refresh_error:
                    self.log_callback(f"页面刷新失败: {str(refresh_error)}", "WARNING")

                self.log_callback("浏览器已显示", "INFO")

            return self.browser_visible

        except Exception as e:
            self.log_callback(f"切换浏览器显示失败: {str(e)}", "ERROR")
            return False

    def show_browser_for_manual_verify(self):
        """显示浏览器进行手动验证"""
        try:
            if not self.browser or not self.tab1:
                self.log_callback("浏览器未初始化", "WARNING")
                return False

            # 强制显示浏览器
            self.tab1.set.window.show()
            self.browser_visible = True

            # 检查并处理白屏问题
            try:
                current_url = self.tab1.url
                page_title = self.tab1.title

                # 检查是否为空白页面
                if (not current_url or current_url == "about:blank" or "data:" in current_url or
                    not page_title or page_title.strip() == ""):

                    self.log_callback("检测到空白页面，正在重新加载...", "INFO")

                    # 重新访问当前应该在的页面
                    if hasattr(self, 'current_page_url') and self.current_page_url:
                        self.tab1.get(self.current_page_url)
                    else:
                        # 默认访问拼多多登录页面
                        domain = self.config.get("domain", "mobile.pinduoduo.com")
                        self.tab1.get(f'https://{domain}/login.html')

                    # 等待页面加载
                    self.log_callback("页面已重新加载", "INFO")
                else:
                    # 页面正常，刷新一下确保显示正确
                    self.tab1.refresh()

            except Exception as page_error:
                self.log_callback(f"页面检查失败: {str(page_error)}", "WARNING")
                # 尝试访问默认页面
                try:
                    domain = self.config.get("domain", "mobile.pinduoduo.com")
                    self.tab1.get(f'https://{domain}')
                except:
                    pass

            self.log_callback("浏览器已显示，请手动完成验证", "INFO")
            self.log_callback("提示：完成验证后可以点击'隐藏浏览器'按钮", "INFO")

            return True

        except Exception as e:
            self.log_callback(f"显示浏览器失败: {str(e)}", "ERROR")
            return False

    def wait_for_manual_action(self, message="请手动完成操作", timeout=300):
        """等待用户手动操作"""
        try:
            # 显示浏览器
            self.show_browser_for_manual_verify()

            self.log_callback(f"{message}，等待中...", "INFO")

            # 这里可以添加一个简单的等待机制
            # 或者让用户点击按钮继续
            import time
            start_time = time.time()

            while time.time() - start_time < timeout:
                if self.should_stop:
                    self.log_callback("用户停止操作", "INFO")
                    return False


            self.log_callback("手动操作超时", "WARNING")
            return False

        except Exception as e:
            self.log_callback(f"等待手动操作失败: {str(e)}", "ERROR")
            return False

    def close(self):
        """关闭浏览器并释放端口"""
        try:
            if self.browser:
                # 强制关闭浏览器进程
                self.browser.quit()
                self.log_callback("浏览器已关闭", "INFO")

                # 等待端口释放
                time.sleep(1)

                # 验证端口是否已释放
                if self.last_successful_port:
                    self.log_callback(f"端口 {self.last_successful_port} 已释放", "INFO")


            # 清理浏览器引用
            self.browser = None
            self.tab1 = None
            self.tab2 = None

        except Exception as e:
            self.log_callback(f"关闭浏览器失败: {str(e)}", "ERROR")

    def batch_process_product_urls(self, urls_file: str) -> Dict[str, int]:
        """
        批量处理商品详情页链接

        Args:
            urls_file: 包含商品链接的文件路径，每行一个链接

        Returns:
            Dict: 处理结果统计 {"success": 成功数量, "failed": 失败数量}
        """
        try:
            # 重置停止标志
            self.should_stop = False

            self.log_callback(f"开始批量处理商品链接文件: {urls_file}", "INFO")
            self.status_callback("正在批量处理商品链接...")

            success_count = 0
            failed_count = 0

            # 检查浏览器是否初始化
            if not self.browser or not self.tab1:
                self.log_callback("浏览器未初始化，正在重新初始化...", "WARNING")
                if not self._reinit_browser_if_needed():
                    self.log_callback("浏览器初始化失败，无法处理", "ERROR")
                    return {"success": 0, "failed": 1}

            # 读取商品链接文件
            if not os.path.exists(urls_file):
                self.log_callback(f"链接文件不存在: {urls_file}", "ERROR")
                return {"success": 0, "failed": 1}

            with open(urls_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()

                # 以 https 作为链接开头来分割
                urls = []

                # 按 https:// 分割内容
                parts = content.split('https://')

                for part in parts[1:]:  # 跳过第一个空元素
                    # 重新组装完整的链接
                    url = 'https://' + part.strip()

                    # 找到链接的结束位置（下一个空格、换行或文件结束）
                    # 按空格分割，取第一部分作为完整链接
                    if ' ' in url:
                        url = url.split(' ')[0]
                    if '\n' in url:
                        url = url.split('\n')[0]
                    if '\t' in url:
                        url = url.split('\t')[0]

                    # 移除可能的尾部标点符号
                    while url and url[-1] in ',.;':
                        url = url[:-1]

                    # 验证是否为有效的商品链接
                    if 'goods_id=' in url:
                        urls.append(url)

            if not urls:
                self.log_callback("链接文件为空", "WARNING")
                return {"success": 0, "failed": 0}

            self.log_callback(f"共找到 {len(urls)} 个商品链接", "INFO")

            # 处理每个链接
            for index, url in enumerate(urls, 1):
                if self.should_stop:
                    self.log_callback("用户停止处理", "INFO")
                    break

                self.status_callback(f"正在处理第 {index}/{len(urls)} 个商品...")
                self.log_callback(f"处理商品链接 {index}/{len(urls)}: {url}", "INFO")

                try:
                    # 再次检查停止标志
                    if self.should_stop:
                        self.log_callback("用户停止处理", "INFO")
                        break

                    # 检查链接格式
                    if 'goods.html?goods_id=' not in url and 'goods_id=' not in url:
                        self.log_callback(f"无效的商品链接格式: {url}", "WARNING")
                        failed_count += 1
                        continue

                    # 处理单个商品
                    result = self.fetch_single_product(url)

                    if result:
                        success_count += 1
                        self.log_callback(f"商品处理成功: {url}", "SUCCESS")
                    else:
                        failed_count += 1
                        return {"success": success_count, "failed": failed_count}

                        # 如果是需要重新登录，停止处理
                    if self.should_stop:
                        self.log_callback("检测到需要重新登录，停止批量处理", "WARNING")
                        return {"success": success_count, "failed": failed_count}

                    # 延时
                    if not self._interruptible_sleep(self._get_delay('normal')):
                        self.log_callback("用户停止处理，中断延时", "INFO")
                        break

                except Exception as e:
                    failed_count += 1
                    self.log_callback(f"处理商品链接出错 {url}: {str(e)}", "ERROR")

            result = {"success": success_count, "failed": failed_count}
            self.log_callback(f"批量处理完成，成功: {success_count}, 失败: {failed_count}", "SUCCESS")
            self.status_callback("批量处理完成")

            return result

        except Exception as e:
            self.log_callback(f"批量处理出错: {str(e)}", "ERROR")
            self.status_callback("批量处理失败")
            return {"success": 0, "failed": 1}



    def __del__(self):
        """析构函数，确保浏览器被关闭"""
        try:
            if hasattr(self, 'browser') and self.browser:
                self.close()
        except:
            pass  # 析构时忽略错误
