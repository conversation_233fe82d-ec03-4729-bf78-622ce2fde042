# -*- mode: python ; coding: utf-8 -*-
from PyInstaller.utils.hooks import collect_data_files
from PyInstaller.utils.hooks import collect_submodules

datas = [('core', 'core'), ('gui', 'gui'), ('Chrome', 'Chrome'), ('license_system', 'license_system')]
hiddenimports = ['DrissionPage', 'DrissionPage.common', 'tkinter', 'tkinter.ttk', 'tkinter.messagebox', 'tkinter.filedialog', 'requests', 'json', 'threading', 'queue', 'datetime', 'hashlib', 'base64', 'license_system', 'license_system.license_manager', 'license_system.license_interface', 'license_system.crypto_utils', 'license_system.hardware_info', 'license_system.ui_integration', 'license_system.config', 'license_system.exceptions', 'cryptography', 'cryptography.fernet', 'cryptography.hazmat.primitives']
datas += collect_data_files('DrissionPage')
hiddenimports += collect_submodules('DrissionPage')


a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='拼多多采集工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
