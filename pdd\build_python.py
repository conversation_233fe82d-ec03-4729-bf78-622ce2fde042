"""
Python打包脚本 - 自动下载和安装环境
"""
import os
import sys
import subprocess
import shutil
import urllib.request
import zipfile
import json
from pathlib import Path

def install_package(package_name, import_name=None):
    """安装Python包"""
    if import_name is None:
        import_name = package_name

    try:
        __import__(import_name)
        print(f"✅ {package_name} already installed")
        return True
    except ImportError:
        print(f"📦 Installing {package_name}...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", package_name],
                         check=True, capture_output=True)
            print(f"✅ {package_name} installed successfully")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {package_name}: {e}")
            return False

def download_chrome():
    """下载便携版Chrome"""
    chrome_dir = Path("Chrome")
    chrome_exe = chrome_dir / "chrome.exe"

    if chrome_exe.exists():
        print("✅ Chrome already exists")
        return True

    print("🌐 Chrome not found, downloading portable Chrome...")

    # Chrome便携版下载链接（这里使用一个示例链接，实际可能需要更新）
    chrome_urls = [
        "https://dl.google.com/chrome/install/googlechromestandaloneenterprise64.msi",
        "https://www.google.com/chrome/browser/desktop/index.html?standalone=1"
    ]

    print("⚠️  Automatic Chrome download is complex due to Google's download restrictions.")
    print("📋 Please manually download Chrome:")
    print("1. Visit: https://www.google.com/chrome/browser/desktop/index.html?standalone=1")
    print("2. Download the standalone installer")
    print("3. Extract or install to ./Chrome/ directory")
    print("4. Ensure Chrome/chrome.exe exists")

    choice = input("\n已手动下载Chrome？(y/n): ").lower()
    return choice == 'y'

def check_environment():
    """检查并自动安装环境"""
    print("🔍 Checking and installing environment...")

    # 检查Python
    try:
        print(f"✅ Python {sys.version}")
    except:
        print("❌ Python check failed")
        return False

    # 检查pip
    try:
        import pip
        print("✅ pip available")
    except ImportError:
        print("📦 Installing pip...")
        subprocess.run([sys.executable, "-m", "ensurepip", "--upgrade"])

    # 安装必要的包
    required_packages = [
        ("DrissionPage", "DrissionPage"),
        ("pyinstaller", "PyInstaller"),
        ("requests", "requests"),
    ]

    all_installed = True
    for package_name, import_name in required_packages:
        if not install_package(package_name, import_name):
            all_installed = False

    return all_installed

def clean_build():
    """清理构建目录"""
    print("Cleaning build directories...")
    
    dirs_to_clean = ['dist', 'build']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"Removed {dir_name}")
    
    # 清理spec文件
    for file in os.listdir('.'):
        if file.endswith('.spec'):
            os.remove(file)
            print(f"Removed {file}")

def build_executable():
    """构建可执行文件"""
    print("Starting build...")
    
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",
        "--windowed", 
        "--name", "PDD_Spider",
        "--collect-submodules=DrissionPage",
        "--collect-data=DrissionPage",
        "--hidden-import=tkinter",
        "--hidden-import=requests",
        "--hidden-import=json",
        "--hidden-import=threading",
        "main.py"
    ]
    
    # 如果Chrome目录存在，添加到打包中
    if os.path.exists("Chrome"):
        cmd.extend(["--add-data", "Chrome;Chrome"])
        print("Including Chrome directory")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("Build successful!")
        return True
    except subprocess.CalledProcessError as e:
        print("Build failed!")
        print("Error:", e.stderr)
        return False

def create_requirements_txt():
    """创建requirements.txt文件"""
    requirements = [
        "DrissionPage>=4.0.0",
        "requests>=2.25.0",
        "pyinstaller>=5.0.0",
    ]

    with open("requirements.txt", "w", encoding="utf-8") as f:
        f.write("\n".join(requirements))

    print("✅ Created requirements.txt")

def install_from_requirements():
    """从requirements.txt安装依赖"""
    if os.path.exists("requirements.txt"):
        print("📦 Installing from requirements.txt...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"],
                         check=True)
            print("✅ All requirements installed")
            return True
        except subprocess.CalledProcessError:
            print("❌ Failed to install requirements")
            return False
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 PDD Spider Auto-Build Script")
    print("=" * 60)

    # 创建requirements文件
    create_requirements_txt()

    # 检查并安装环境
    print("\n📋 Step 1: Environment Setup")
    if not check_environment():
        print("❌ Environment setup failed")
        print("💡 Trying alternative installation...")
        if not install_from_requirements():
            print("❌ All installation methods failed")
            input("Press Enter to exit...")
            return

    # 检查Chrome
    print("\n📋 Step 2: Chrome Setup")
    if not download_chrome():
        print("⚠️  Chrome not configured, will use system Chrome")

    # 清理构建目录
    print("\n📋 Step 3: Clean Build")
    clean_build()

    # 构建可执行文件
    print("\n📋 Step 4: Build Executable")
    if build_executable():
        exe_path = "dist/PDD_Spider.exe"
        if os.path.exists(exe_path):
            size = os.path.getsize(exe_path)
            print(f"\n🎉 Build Successful!")
            print(f"📁 File: {exe_path}")
            print(f"📊 Size: {size:,} bytes ({size/1024/1024:.1f} MB)")

            # 测试可执行文件
            print("\n🧪 Testing executable...")
            print("💡 You can now test the executable by running it")
        else:
            print("⚠️  Warning: Executable not found")
    else:
        print("❌ Build failed!")
        print("💡 Check the error messages above")

    print("\n" + "=" * 60)
    input("Press Enter to exit...")

if __name__ == "__main__":
    main()
