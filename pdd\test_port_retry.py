#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试浏览器端口重试功能
"""

import sys
import os
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.spider import Pdd<PERSON>pider


def test_port_retry():
    """测试端口重试功能"""
    print("=" * 50)
    print("测试浏览器端口重试功能")
    print("=" * 50)
    
    def log_callback(message, level):
        print(f"[{level}] {message}")
    
    def progress_callback(progress):
        print(f"Progress: {progress}")
    
    def status_callback(status):
        print(f"Status: {status}")
    
    # 创建爬虫实例
    config = {
        "headless": False,  # 显示浏览器以便观察
        "delay_min": 1,
        "delay_max": 2,
        "retry_times": 2
    }
    
    spider = PddSpider(
        config=config,
        log_callback=log_callback,
        progress_callback=progress_callback,
        status_callback=status_callback
    )
    
    try:
        print("\n1. 测试初始化浏览器...")
        if spider.browser and spider.tab1:
            print("✅ 浏览器初始化成功")
            
            # 测试访问页面
            print("\n2. 测试访问页面...")
            spider.tab1.get('https://www.baidu.com')
            time.sleep(2)
            
            title = spider.tab1.title
            print(f"✅ 页面标题: {title}")
            
            # 测试端口信息
            print(f"\n3. 当前使用端口: {spider.browser.address}")
            
            # 测试重新初始化
            print("\n4. 测试重新初始化浏览器...")
            if spider._reinit_browser_if_needed():
                print("✅ 重新初始化成功")
                print(f"新端口: {spider.browser.address}")
            else:
                print("❌ 重新初始化失败")
            
        else:
            print("❌ 浏览器初始化失败")
            
    except Exception as e:
        print(f"❌ 测试过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理
        try:
            if spider.browser:
                spider.close()
                print("\n✅ 浏览器已关闭")
        except:
            pass
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)


def test_port_availability():
    """测试端口可用性检查"""
    print("\n" + "=" * 50)
    print("测试端口可用性检查")
    print("=" * 50)
    
    spider = PddSpider()
    
    # 测试端口检查
    test_ports = [9222, 9223, 9224, 9225]
    for port in test_ports:
        available = spider._is_port_available(port)
        status = "可用" if available else "占用"
        print(f"端口 {port}: {status}")
    
    # 测试获取可用端口
    available_ports = spider._get_available_ports(start_port=9222, count=5)
    print(f"\n可用端口列表: {available_ports}")


if __name__ == "__main__":
    print("选择测试项目:")
    print("1. 测试端口重试功能")
    print("2. 测试端口可用性检查")
    print("3. 全部测试")
    
    choice = input("请输入选择 (1/2/3): ").strip()
    
    if choice == "1":
        test_port_retry()
    elif choice == "2":
        test_port_availability()
    elif choice == "3":
        test_port_availability()
        test_port_retry()
    else:
        print("无效选择")
    
    input("\n按 Enter 键退出...")
