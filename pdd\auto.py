import json
import os.path
import random
# from yezi import api
import re
import sys
import time

import requests
from DrissionPage import Chromium, ChromiumOptions
from DrissionPage.common import Settings


class spider_pdd():
    def __init__(self):
        co = ChromiumOptions().incognito()
        self.browser = Chromium(co)

        Settings.set_singleton_tab_obj(False)

        self.tab1 = self.browser.get_tab(1)
        self.tab2 = self.browser.get_tab(1)

        self.tab1.set.window.show()


        self.page_id = None

        self.cookies = None


    def clean_detail_data(self, id, name, text):

        result_path = f'./result/{id}.json'
        cache_path = './cache/ids.json'

        pattern = r"window\.rawData\s*=\s*(.*?)\s*<\/"  # 匹配到最近的 </
        match = re.search(pattern, text)

        result_dirname = os.path.dirname(result_path)
        cache_dirname = os.path.dirname(cache_path)

        if match:
            data_str = match.group(1)
            data_dict = json.loads(data_str.rstrip(';'))
            try:
                if 'goods' not in data_dict['store']['initDataObj']:
                    print('需要重新登录.........')
                    self.true_login()
                    self.tab1.get(self.url)
                    self.auto_spider()
                elif data_dict['store']['initDataObj']['goods']['statusExplain'] == "商品已售罄，推荐以下相似商品":
                    print('账号被风控.......')
                    self.true_login()

                os.makedirs(result_dirname, exist_ok=True)

                with open(result_path, 'w', encoding='utf-8') as f:
                    json.dump(data_dict, f, indent=2, ensure_ascii=False)
                print(f'{id}抓取成功!!')

                os.makedirs(cache_dirname, exist_ok=True)
                try:
                    with open(cache_path,'r',encoding='utf-8') as f:
                        pre_cache_dict = json.load(f)
                except:
                    pre_cache_dict = {}

                pre_cache_dict[id] = name
                with open(cache_path, 'w', encoding='utf-8') as file:
                    json.dump(pre_cache_dict, file, indent=2, ensure_ascii=False)
                print(f'{id}:{name}缓存成功!!')
                return True

            except Exception as e:
                print('请手动进行滑块验证......\n\n')
                self.tab1.set.window.show()


    def true_login(self):
        while True:
            self.tab1.get('https://mobile.pinduoduo.com/login.html')

            login_button = self.tab1.ele('x://*[@id="first"]/div[2]/div')
            login_button.click()

            confirm = self.tab1.ele('x://*[@id="container"]/form/div[2]/p/i')
            confirm.click()

            number = self.tab1.ele('@id=user-mobile')
            number.clear()

            while True:
                ##输入手机号码
                phone = input('输入手机号码.......:')
                if len(phone) != 11 or not phone.isdigit():
                    print('输入手机格式有误........')
                    number.clear()
                    continue
                else:
                    break

            number.input(phone)

            send_code_button = self.tab1.ele('@id=code-button')
            send_code_button.click()

            while True:
                ##输入验证码
                phone_code_button = self.tab1.ele('@id=code-button', timeout=2)
                if phone_code_button:
                    phone_code_button.click()

                popup = self.tab1.ele('@class=_3E7ieRE-',timeout=2)
                if popup:
                    print('此次验证码为语言验证码，请留意来电........')
                    popup.click()

                code = input('请输入验证码(若为收到验证码，直接回车重新获取)....:')
                if not code:
                    self.tab1.refresh()
                    time.sleep(random.randint(3,5))

                    confirm = self.tab1.ele('x://*[@id="container"]/form/div[2]/p/i')
                    confirm.click()

                elif len(code) != 6 or not code.isdigit():  # 假设验证码是6位数字
                    print("验证码格式不正确，请重新输入")
                    continue

                else:
                    break

            self.tab1.ele('@id=input-code').input(code)

            self.tab1.listen.start('/v3?pdduid')

            login = self.tab1.ele('@id=submit-button')
            login.click()

            content = self.tab1.ele('@class=KNVRSU85', timeout=5)
            if content:
                print('登录成功!!!')
                break

            print('登录失败.......')

        time.sleep(random.randint(1,3))
        self.tab1.refresh()
        pre_cookies = self.tab1.cookies()
        first_page_url = self.tab1.url

        cookies = {}
        cookies.update({x['name']: x['value'] for x in pre_cookies})

        open('cookies.json', 'w', encoding='utf-8').write(json.dumps(cookies, indent=2, ensure_ascii=False))
        self.cookies = cookies

        open('url.txt', 'w', encoding='utf-8').write(first_page_url)
        self.url = first_page_url

        print('获取到cookies和首页网址!!!!!')

    def clean_recom_first_page(self, text: str):
        try:
            pattern = r"window\.rawData\s*=\s*(.*?)\s*;"
            match = re.search(pattern, text)

            if match:
                data_str = match.group(1)
                data_dict = json.loads(data_str.rstrip(';'))

                goods_info = data_dict["stores"]["store"]["mainListProps"]["goodsList"]

                goods_dict = {}
                for good in goods_info:
                    goods_name = good.get("data")('goods_name')
                    goods_price = good["data"]["group"]["price_info"]

                    goods_dict[goods_name] = goods_price

                return goods_dict

            return {}
        except Exception as e:
            return {}

    def clean_extra_recom_page(self, text: dict):
        """
        清理额外页面的商品数据

        Args:
            text: 页面响应的JSON数据

        Returns:
            Dict[str, str]: 商品ID到商品名称的映射
        """
        try:
            goods_info = text.get("data").get("goods_list")
            goods_dict = {}
            goods_dict.update({good.get('data').get('goods_id'): good.get('data').get('goods_name') for good in goods_info})
            return goods_dict
        except Exception as e:
            return {}


    def detail_spider(self, goods_name):
        good_button = self.tab1.ele(f'@text()={goods_name}', timeout=5)

        if good_button:
            self.tab2.listen.start('goods.html?goods_id')

            time.sleep(random.randint(3, 5))
            good_button.click()

            Resposne = self.tab2.listen.wait(timeout=5)

            while not Resposne:
                self.tab1.scroll.to_bottom()

                time.sleep(random.randint(1, 3))

                good_button = self.tab1.ele(f'@text()={goods_name}', timeout=5)

                if not good_button:
                    self.tab1.back()
                    good_button = self.tab1.ele(f'@text()={goods_name}', timeout=5)

                self.tab2.listen.start('goods.html?goods_id')

                good_button.click()

                Resposne = self.tab2.listen.wait(timeout=5)

            detail_id = Resposne.response.url.split('&')[0].split('=')[1]

            result = self.clean_detail_data(id=detail_id, name=goods_name, text=Resposne.response.body)

            """判断是否有滑块验证"""
            while not result:
                self.tab2.listen.start('goods.html?goods_id')
                Resposne = self.tab2.listen.wait()
                result = self.clean_detail_data(id=detail_id, name=goods_name, text=Resposne.response.body)
                if result:
                    # self.tab1.set.window.hide()
                    print('滑块通过!!')

            time.sleep(random.randint(3, 5))

            self.tab1.back()

            return True

    def auto_spider(self, pages=None):
        with open('./cache/ids.json', 'r', encoding='utf-8') as file:
            pre_dict = json.load(file)

        self.tab2.listen.start('https://mobile.pinduoduo.com/')
        self.tab1.get('https://mobile.pinduoduo.com/')

        Response = self.tab2.listen.wait()

        goods_dict = self.clean_recom_first_page(Response.response.body)

        pre_goods = []
        for goods_name, goods_price in goods_dict.items():
            if float(goods_price) < 0.5:
                if goods_name not in pre_goods and goods_name not in pre_dict.values():
                    self.detail_spider(goods_name)

        page = 2
        while page <= pages:
            self.tab2.listen.start('/v3?pdduid=')
            print(f"正在下滑第{page}页............")
            self.tab1.scroll.to_bottom()

            Response = self.tab2.listen.wait()
            goods_dict = self.clean_extra_recom_page(Response.response.body)

            """查看是否有更多推荐按钮"""
            recommend_button = self.tab1.ele('@class=wP9L9TiU', timeout=2)

            if recommend_button:
                goods_name = self.tab1.s_eles('@class=J9WPy2Wu', timeout=2)
                if goods_name:
                    goods_name = goods_name.get.texts()
                else:

                    """进入推荐页"""
                    goods_name = self.tab1.s_eles('@class=jzcJiRHO', timeout=2)
                    goods_name = goods_name.get.texts()

                print(f"共获取{len(goods_name)}件商品！！")

                for name in goods_name:
                    if name not in pre_goods and name not in pre_dict.values():
                        good_button = self.tab1.ele(f'@text()={name}', timeout=5)
                        if good_button:
                            self.tab2.listen.start('goods.html?goods_id')

                            time.sleep(random.randint(3, 5))
                            good_button.click()

                            Resposne = self.tab2.listen.wait(timeout=5)

                            while not Resposne:
                                self.tab1.scroll.to_bottom()

                                time.sleep(random.randint(1, 3))

                                good_button = self.tab1.ele(f'@text()={name}', timeout=5)

                                if not good_button:
                                    self.tab1.back()
                                    good_button = self.tab1.ele(f'@text()={name}', timeout=5)

                                self.tab2.listen.start('goods.html?goods_id')

                                good_button.click()

                                Resposne = self.tab2.listen.wait(timeout=5)

                            detail_id = Resposne.response.url.split('&')[0].split('=')[1]

                            result = self.clean_detail_data(id=detail_id, name=name, text=Resposne.response.body)

                            """判断是否有滑块验证"""
                            while not result:
                                self.tab2.listen.start('goods.html?goods_id')
                                Resposne = self.tab2.listen.wait()
                                result = self.clean_detail_data(id=detail_id, name=name, text=Resposne.response.body)
                                if result:
                                    # self.tab1.set.window.hide()
                                    print('滑块通过!!')

                            goods_count += 1
                            print(f'成功爬取{goods_count}条数据!!')
                            pre_goods.append(name)
                            time.sleep(random.randint(3, 5))

                            self.tab1.back()
                            open('url.txt', 'w', encoding='utf-8').write(self.tab1.url)

                        else:
                            self.tab1.back()
                            continue
                    else:
                        print(f'跳过{name}.......')
                        continue

                print("进入推荐页..........")
                recommend_button = self.tab1.ele('@class=wP9L9TiU', timeout=2)
                recommend_button.click()
                page += 1

            else:
                self.tab1.scroll.to_bottom()

            page += 1




    def run(self):
        result = input('是否需要完整登录?(1为是)')

        if result == '1':
            self.true_login()
        else:
            path = 'url.txt'
            if os.path.exists(path):
                pass
            else:
                print('未有先前记录保存，需要重新登录.......')
                self.true_login()

        pages = input('请输入要爬取的页数(至少为1).....')

        self.auto_spider(pages=int(pages))



if __name__ == '__main__':
    pdd = spider_pdd()
    pdd.run()
