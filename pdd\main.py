"""
拼多多商品数据采集工具 - 主程序入口
"""
import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import json

# 处理打包环境的路径问题
if getattr(sys, 'frozen', False):
    # 打包后的exe环境
    application_path = os.path.dirname(sys.executable)
    # 设置工作目录为exe所在目录
    os.chdir(application_path)
else:
    # 开发环境
    application_path = os.path.dirname(os.path.abspath(__file__))

# 添加项目路径到sys.path
sys.path.insert(0, application_path)

from gui.main_window import MainWindow
from gui.login_dialog import show_login_dialog
from core.spider import PddSpider
from core.data_processor import DataProcessor
from fix_white_screen import apply_white_screen_fix


class PddSpiderApp:
    def __init__(self):
        """初始化应用程序"""
        self.spider = None
        self.main_window = None
        
        # 创建主窗口
        self.setup_main_window()
        
        # 绑定事件处理
        self.bind_events()
        
        # 初始化数据
        self.initialize_data()
        
    def setup_main_window(self):
        """设置主窗口"""
        self.main_window = MainWindow()
        
        # 设置窗口关闭事件
        self.main_window.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def bind_events(self):
        """绑定事件处理"""
        # 登录相关 - 使用属性绑定方式
        self.main_window._login_handler = self.handle_login
        self.main_window._check_login_handler = self.handle_check_login_status
        self.main_window._clear_login_handler = self.handle_clear_login

        # 采集相关
        self.main_window._start_collect_handler = self.handle_start_collect
        self.main_window._stop_collect_handler = self.handle_stop_collect
        
        # 数据管理相关
        self.main_window._refresh_stats_handler = self.handle_refresh_stats
        self.main_window.refresh_data_handler = self.handle_refresh_data

        # 浏览器控制相关
        self.main_window._toggle_browser_handler = self.handle_toggle_browser
        self.main_window._manual_verify_handler = self.handle_manual_verify
        self.main_window.export_data = self.handle_export_data
        self.main_window.delete_selected_handler = self.handle_delete_selected
        self.main_window.view_details_handler = self.handle_view_details
        
        # 其他功能
        self.main_window.clear_cache = self.handle_clear_cache
        self.main_window.show_statistics = self.handle_show_statistics

        # 配置更新
        self.main_window._update_spider_config_handler = self.update_spider_config
        
    def initialize_data(self):
        """初始化数据"""
        # 刷新统计信息
        self.handle_refresh_stats()
        
        # 刷新数据预览
        self.handle_refresh_data()
        
        # 检查登录状态
        self.handle_check_login_status()
        
    def get_config(self):
        """获取当前配置"""
        try:
            return {
                "headless": self.main_window.headless_var.get(),
                "delay_min": int(self.main_window.delay_min_var.get()),
                "delay_max": int(self.main_window.delay_max_var.get()),
                "retry_times": int(self.main_window.retry_var.get()),
                "result_path": self.main_window.result_path_var.get(),
                "cache_path": "./cache/ids.json",
                "cookies_path": "./cookies.json",
                "url_path": "./url.txt",
                "domain": "mobile.pinduoduo.com",
                # 价格筛选配置
                "price_filter": {
                    "enabled": self.main_window.price_filter_enabled_var.get(),
                    "mode": self.main_window.price_filter_mode.get(),
                    "min_price": float(self.main_window.min_price_var.get()),
                    "max_price": float(self.main_window.max_price_var.get())
                },
                # 虚拟商品筛选配置
                "virtual_goods_filter": {
                    "enabled": self.main_window.virtual_filter_enabled_var.get(),
                    "mode": self.main_window.virtual_filter_mode.get(),
                    "keywords": [
                        "充值", "会员", "虚拟", "话费", "流量", "Q币", "点券", "代充",
                        "游戏币", "钻石", "金币", "积分", "卡密", "激活码", "兑换码",
                        "网费", "电费", "水费", "燃气费", "缴费", "代缴", "充值卡",
                        "游戏充值", "手游", "端游", "网游", "在线充值", "自动发货"
                    ]
                }
            }
        except ValueError as e:
            # 如果价格输入无效，使用默认值
            self.main_window.log_message(f"配置获取失败，使用默认值: {str(e)}", "WARNING")
            return {
                "headless": True,
                "delay_min": 3,
                "delay_max": 5,
                "retry_times": 3,
                "result_path": "./result/",
                "cache_path": "./cache/ids.json",
                "cookies_path": "./cookies.json",
                "url_path": "./url.txt",
                "domain": "mobile.pinduoduo.com",
                "price_filter": {
                    "enabled": True,
                    "mode": "range",
                    "min_price": 0.0,
                    "max_price": 0.5
                },
                "virtual_goods_filter": {
                    "enabled": True,
                    "mode": "exclude",
                    "keywords": [
                        "充值", "会员", "虚拟", "话费", "流量", "Q币", "点券", "代充",
                        "游戏币", "钻石", "金币", "积分", "卡密", "激活码", "兑换码",
                        "网费", "电费", "水费", "燃气费", "缴费", "代缴", "充值卡",
                        "游戏充值", "手游", "端游", "网游", "在线充值", "自动发货"
                    ]
                }
            }
        
    def init_spider(self):
        """初始化爬虫实例"""
        if self.spider is None:
            try:
                config = self.get_config()
                self.spider = PddSpider(
                    config=config,
                    log_callback=self.main_window.log_message,
                    progress_callback=lambda msg: self.main_window.progress_var.set(msg),
                    status_callback=self.main_window.update_status
                )

                # 应用白屏修复
                try:
                    apply_white_screen_fix(self.spider)
                    self.main_window.log_message("白屏修复功能已启用", "INFO")
                except Exception as e:
                    self.main_window.log_message(f"白屏修复功能启用失败: {str(e)}", "WARNING")

                self.main_window.log_message("爬虫初始化成功", "SUCCESS")
                return True
            except Exception as e:
                self.main_window.log_message(f"爬虫初始化失败: {str(e)}", "ERROR")
                messagebox.showerror("错误", f"爬虫初始化失败: {str(e)}")
                return False
        return True

    def update_spider_config(self):
        """更新爬虫配置"""
        if self.spider:
            try:
                # 获取最新配置
                current_config = self.get_config()

                # 更新价格筛选配置
                price_filter = current_config.get("price_filter", {})
                self.spider.update_price_filter(
                    enabled=price_filter.get("enabled"),
                    min_price=price_filter.get("min_price"),
                    max_price=price_filter.get("max_price"),
                    mode=price_filter.get("mode")
                )

                # 更新虚拟商品筛选配置
                virtual_filter = current_config.get("virtual_goods_filter", {})
                self.spider.update_virtual_goods_filter(
                    enabled=virtual_filter.get("enabled"),
                    mode=virtual_filter.get("mode"),
                    keywords=virtual_filter.get("keywords")
                )

                # 更新其他基本配置
                self.spider.config.update({
                    "delay_min": current_config.get("delay_min"),
                    "delay_max": current_config.get("delay_max"),
                    "retry_times": current_config.get("retry_times")
                })

                self.main_window.log_message("爬虫配置已更新", "INFO")

            except Exception as e:
                self.main_window.log_message(f"更新爬虫配置失败: {str(e)}", "ERROR")

    def handle_login(self):
        """处理登录"""
        if not self.init_spider():
            return
            
        try:
            # 显示登录对话框
            success = show_login_dialog(self.main_window.root, self.spider)
            
            if success:
                self.main_window.login_status_var.set("已登录")
                self.main_window.is_logged_in = True
                # 更新域名显示
                current_domain = self.spider.config.get("domain", "mobile.pinduoduo.com")
                self.main_window.domain_status_var.set(f"当前域名: {current_domain}")
                self.main_window.log_message("登录成功", "SUCCESS")
            else:
                self.main_window.login_status_var.set("未登录")
                self.main_window.is_logged_in = False
                
        except Exception as e:
            self.main_window.log_message(f"登录失败: {str(e)}", "ERROR")
            messagebox.showerror("错误", f"登录失败: {str(e)}")
            
    def handle_check_login_status(self):
        """检查登录状态"""
        if not self.init_spider():
            return
            
        try:
            is_logged_in = self.spider.check_login_status()
            
            if is_logged_in:
                self.main_window.login_status_var.set("已登录")
                self.main_window.is_logged_in = True
                # 更新域名显示
                current_domain = self.spider.config.get("domain", "mobile.pinduoduo.com")
                self.main_window.domain_status_var.set(f"当前域名: {current_domain}")
                self.main_window.log_message("登录状态有效", "SUCCESS")
            else:
                self.main_window.login_status_var.set("未登录")
                self.main_window.is_logged_in = False
                self.main_window.log_message("未登录或登录已过期", "WARNING")
                
        except Exception as e:
            self.main_window.log_message(f"检查登录状态失败: {str(e)}", "ERROR")
            
    def handle_clear_login(self):
        """清除登录信息"""
        try:
            # 删除登录文件
            files_to_remove = ["./cookies.json", "./url.txt"]
            for file_path in files_to_remove:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    
            self.main_window.login_status_var.set("未登录")
            self.main_window.is_logged_in = False
            self.main_window.log_message("登录信息已清除", "SUCCESS")
            messagebox.showinfo("成功", "登录信息已清除")
            
        except Exception as e:
            self.main_window.log_message(f"清除登录信息失败: {str(e)}", "ERROR")
            messagebox.showerror("错误", f"清除登录信息失败: {str(e)}")
            
    def handle_start_collect(self):
        """开始采集"""
        if not self.main_window.is_logged_in:
            messagebox.showwarning("警告", "请先登录账号")
            return
            
        if not self.init_spider():
            return
            
        # 检查是否正在采集
        if self.main_window.is_collecting:
            messagebox.showwarning("警告", "正在采集中，请等待完成")
            return
            
        # 获取采集模式
        mode = self.main_window.collect_mode.get()
        
        if mode == "batch":
            # 批量采集
            try:
                pages = int(self.main_window.pages_var.get())
                if pages <= 0:
                    messagebox.showerror("错误", "页数必须大于0")
                    return

                self.start_batch_collect(pages)

            except ValueError:
                messagebox.showerror("错误", "请输入有效的页数")

        elif mode == "single":
            # 单品采集
            detail_url = self.main_window.detail_url.get().strip()
            if not detail_url:
                messagebox.showerror("错误", "请输入商品链接")
                return

            self.start_single_collect(detail_url)

        elif mode == "batch_urls":
            # 批量链接采集
            urls_file = self.main_window.urls_file_var.get().strip()
            if not urls_file:
                messagebox.showerror("错误", "请选择商品链接文件")
                return

            if not os.path.exists(urls_file):
                messagebox.showerror("错误", "链接文件不存在")
                return

            self.start_batch_urls_collect(urls_file)
            
    def start_batch_collect(self, pages):
        """开始批量采集"""
        def collect_thread():
            try:
                self.main_window.is_collecting = True
                self.main_window.start_btn.config(state=tk.DISABLED)
                self.main_window.stop_btn.config(state=tk.NORMAL)
                self.main_window.progress_bar.start()

                # 更新爬虫配置
                self.update_spider_config()

                # 执行采集
                result = self.spider.batch_collect(pages)

                # 更新UI
                self.main_window.root.after(0, self.collect_completed, result)

            except Exception as e:
                self.main_window.root.after(0, self.collect_error, str(e))

        thread = threading.Thread(target=collect_thread)
        thread.daemon = True
        thread.start()

    def start_batch_urls_collect(self, urls_file):
        """开始批量链接采集"""
        def collect_thread():
            try:
                self.main_window.is_collecting = True
                self.main_window.start_btn.config(state=tk.DISABLED)
                self.main_window.stop_btn.config(state=tk.NORMAL)
                self.main_window.progress_bar.start()

                # 更新爬虫配置
                self.update_spider_config()

                # 执行批量链接采集
                result = self.spider.batch_process_product_urls(urls_file)

                # 更新UI
                self.main_window.root.after(0, self.collect_completed, result)

            except Exception as e:
                self.main_window.root.after(0, self.collect_error, str(e))

        thread = threading.Thread(target=collect_thread)
        thread.daemon = True
        thread.start()

    def start_single_collect(self, detail_url):
        """开始单品采集"""
        def collect_thread():
            try:
                self.main_window.is_collecting = True
                self.main_window.start_btn.config(state=tk.DISABLED)
                self.main_window.stop_btn.config(state=tk.NORMAL)
                self.main_window.progress_bar.start()

                # 更新爬虫配置
                self.update_spider_config()

                # 执行采集
                success = self.spider.fetch_single_product(detail_url)

                # 更新UI
                result = {"success": 1 if success else 0, "failed": 0 if success else 1}
                self.main_window.root.after(0, self.collect_completed, result)

            except Exception as e:
                self.main_window.root.after(0, self.collect_error, str(e))

        thread = threading.Thread(target=collect_thread)
        thread.daemon = True
        thread.start()
        
    def collect_completed(self, result):
        """采集完成"""
        self.main_window.is_collecting = False
        self.main_window.start_btn.config(state=tk.NORMAL)
        self.main_window.stop_btn.config(state=tk.DISABLED)
        self.main_window.progress_bar.stop()
        
        # 显示结果
        success = result.get("success", 0)
        failed = result.get("failed", 0)
        skipped = result.get("skipped", 0)
        
        message = f"采集完成！成功: {success}, 失败: {failed}"
        if skipped > 0:
            message += f", 跳过: {skipped}"
            
        self.main_window.log_message(message, "SUCCESS")
        messagebox.showinfo("完成", message)
        
        # 刷新数据
        self.handle_refresh_stats()
        self.handle_refresh_data()
        
    def collect_error(self, error_msg):
        """采集错误"""
        self.main_window.is_collecting = False
        self.main_window.start_btn.config(state=tk.NORMAL)
        self.main_window.stop_btn.config(state=tk.DISABLED)
        self.main_window.progress_bar.stop()
        
        self.main_window.log_message(f"采集出错: {error_msg}", "ERROR")
        messagebox.showerror("错误", f"采集出错: {error_msg}")
        
    def handle_stop_collect(self):
        """停止采集"""
        if self.spider and self.main_window.is_collecting:
            self.spider.stop_collect()
            self.main_window.log_message("正在停止采集...", "INFO")

            # 立即更新UI状态
            self.main_window.is_collecting = False
            self.main_window.start_btn.config(state=tk.NORMAL)
            self.main_window.stop_btn.config(state=tk.DISABLED)
            self.main_window.progress_bar.stop()
            self.main_window.progress_var.set("采集已停止")

            self.main_window.log_message("采集已停止", "WARNING")
            
    def handle_refresh_stats(self):
        """刷新统计信息"""
        try:
            self.main_window.log_message("正在刷新统计信息...", "INFO")

            config = self.get_config()
            stats = DataProcessor.get_data_statistics(
                config["result_path"],
                config["cache_path"]
            )

            # 更新主要统计显示
            consistency_status = "✓" if stats['data_consistency'] else "⚠"
            stats_text = f"总计: {stats['cached_products']} 个商品 | 文件: {stats['result_files']} 个 | 大小: {stats['total_size_mb']} MB {consistency_status}"
            self.main_window.stats_var.set(stats_text)

            # 记录详细统计信息到日志
            self.main_window.log_message(f"统计刷新完成 - 商品数量: {stats['cached_products']}", "SUCCESS")
            self.main_window.log_message(f"结果文件: {stats['result_files']} 个", "INFO")
            self.main_window.log_message(f"总大小: {stats['total_size_mb']} MB", "INFO")
            self.main_window.log_message(f"平均文件大小: {stats['avg_file_size_kb']} KB", "INFO")

            if stats['latest_file_time'] != "无":
                self.main_window.log_message(f"最新文件时间: {stats['latest_file_time']}", "INFO")

            if not stats['data_consistency']:
                self.main_window.log_message("⚠ 数据不一致：缓存数量与文件数量不匹配", "WARNING")

        except Exception as e:
            self.main_window.log_message(f"刷新统计失败: {str(e)}", "ERROR")

    def handle_refresh_data(self):
        """刷新数据预览"""
        try:
            config = self.get_config()
            preview_data = DataProcessor.get_product_preview_data(
                config["result_path"],
                config["cache_path"],
                limit=100
            )

            # 清空现有数据
            for item in self.main_window.data_tree.get_children():
                self.main_window.data_tree.delete(item)

            # 添加新数据
            for data in preview_data:
                self.main_window.data_tree.insert("", tk.END, values=(
                    data["ID"],
                    data["商品名称"][:50] + "..." if len(data["商品名称"]) > 50 else data["商品名称"],
                    data["价格"],
                    data["采集时间"]
                ))

            self.main_window.log_message(f"数据预览已刷新，共 {len(preview_data)} 条记录", "SUCCESS")

        except Exception as e:
            self.main_window.log_message(f"刷新数据失败: {str(e)}", "ERROR")

    def handle_export_data(self):
        """导出数据"""
        try:
            config = self.get_config()
            cache_dict = DataProcessor.load_all_ids(config["cache_path"])

            if not cache_dict:
                messagebox.showwarning("警告", "没有数据可导出")
                return

            product_ids = list(cache_dict.keys())
            export_format = self.main_window.export_format.get()

            # 生成文件名
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            if export_format == "csv":
                filename = f"pdd_products_{timestamp}.csv"
                output_path = os.path.join(self.main_window.export_path_var.get(), filename)
                success = DataProcessor.export_to_csv(product_ids, output_path, config["result_path"])
            elif export_format == "excel":
                filename = f"pdd_products_{timestamp}.xlsx"
                output_path = os.path.join(self.main_window.export_path_var.get(), filename)
                success = DataProcessor.export_to_excel(product_ids, output_path, config["result_path"])
            else:  # json
                filename = f"pdd_products_{timestamp}.json"
                output_path = os.path.join(self.main_window.export_path_var.get(), filename)
                success = DataProcessor.export_to_json(product_ids, output_path, config["result_path"])

            if success:
                self.main_window.log_message(f"数据导出成功: {output_path}", "SUCCESS")
                messagebox.showinfo("成功", f"数据已导出到: {output_path}")
            else:
                self.main_window.log_message("数据导出失败", "ERROR")
                messagebox.showerror("失败", "数据导出失败")

        except Exception as e:
            self.main_window.log_message(f"导出数据失败: {str(e)}", "ERROR")
            messagebox.showerror("错误", f"导出数据失败: {str(e)}")

    def handle_delete_selected(self):
        """删除选中数据"""
        try:
            selected_items = self.main_window.data_tree.selection()
            if not selected_items:
                messagebox.showwarning("警告", "请先选择要删除的数据")
                return

            # 确认删除
            count = len(selected_items)
            if not messagebox.askyesno("确认", f"确定要删除选中的 {count} 条数据吗？"):
                return

            config = self.get_config()
            success_count = 0

            for item in selected_items:
                values = self.main_window.data_tree.item(item, "values")
                product_id = values[0]

                if DataProcessor.delete_product_data(product_id, config["result_path"], config["cache_path"]):
                    success_count += 1
                    self.main_window.data_tree.delete(item)

            self.main_window.log_message(f"成功删除 {success_count} 条数据", "SUCCESS")
            messagebox.showinfo("完成", f"成功删除 {success_count} 条数据")

            # 刷新统计
            self.handle_refresh_stats()

        except Exception as e:
            self.main_window.log_message(f"删除数据失败: {str(e)}", "ERROR")
            messagebox.showerror("错误", f"删除数据失败: {str(e)}")

    def handle_view_details(self):
        """查看详情"""
        try:
            selected_items = self.main_window.data_tree.selection()
            if not selected_items:
                messagebox.showwarning("警告", "请先选择要查看的数据")
                return

            # 只查看第一个选中项
            item = selected_items[0]
            values = self.main_window.data_tree.item(item, "values")
            product_id = values[0]

            config = self.get_config()
            product_data = DataProcessor.load_product(product_id, config["result_path"])

            if product_data:
                info = DataProcessor.extract_product_info(product_data)
                self.show_product_details(info)
            else:
                messagebox.showerror("错误", "无法加载商品详情")

        except Exception as e:
            self.main_window.log_message(f"查看详情失败: {str(e)}", "ERROR")
            messagebox.showerror("错误", f"查看详情失败: {str(e)}")

    def show_product_details(self, info):
        """显示商品详情"""
        detail_window = tk.Toplevel(self.main_window.root)
        detail_window.title(f"商品详情 - {info.get('商品名称', '未知商品')}")
        detail_window.geometry("800x600")
        detail_window.transient(self.main_window.root)
        detail_window.grab_set()  # 模态窗口

        # 居中显示
        detail_window.update_idletasks()
        x = (detail_window.winfo_screenwidth() // 2) - (800 // 2)
        y = (detail_window.winfo_screenheight() // 2) - (600 // 2)
        detail_window.geometry(f"800x600+{x}+{y}")

        # 创建主框架
        main_frame = ttk.Frame(detail_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # 标题区域
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 15))

        title_label = ttk.Label(title_frame, text="📦 商品详细信息",
                               font=("Arial", 16, "bold"))
        title_label.pack(side=tk.LEFT)

        # 商品ID标签
        if info.get('商品ID'):
            id_label = ttk.Label(title_frame, text=f"ID: {info.get('商品ID')}",
                                font=("Arial", 10), foreground="gray")
            id_label.pack(side=tk.RIGHT)

        # 创建笔记本控件（标签页）
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)

        # 基本信息标签页
        basic_frame = ttk.Frame(notebook)
        notebook.add(basic_frame, text="📋 基本信息")

        # 创建基本信息的滚动区域
        basic_canvas = tk.Canvas(basic_frame)
        basic_scrollbar = ttk.Scrollbar(basic_frame, orient="vertical", command=basic_canvas.yview)
        basic_scrollable_frame = ttk.Frame(basic_canvas)

        basic_scrollable_frame.bind(
            "<Configure>",
            lambda e: basic_canvas.configure(scrollregion=basic_canvas.bbox("all"))
        )

        basic_canvas.create_window((0, 0), window=basic_scrollable_frame, anchor="nw")
        basic_canvas.configure(yscrollcommand=basic_scrollbar.set)

        # 显示基本信息
        basic_info_fields = [
            ("商品名称", "🏷️"),
            ("价格", "💰"),
            ("店铺名称", "🏪"),
            ("销量提示", "📊"),
            ("商品类型", "📦"),
            ("分类ID", "🏷️"),
            ("品牌ID", "🔖"),
            ("主图数量", "🖼️"),
            ("是否有本地团", "📍"),
            ("状态说明", "ℹ️"),
            ("服务器时间", "⏰")
        ]

        for i, (field, icon) in enumerate(basic_info_fields):
            if field in info:
                value = info[field]
                if field == "价格":
                    value = f"¥{value:.2f}" if isinstance(value, (int, float)) else value
                elif field == "是否有本地团":
                    value = "是" if value else "否"

                # 创建信息行
                info_frame = ttk.Frame(basic_scrollable_frame)
                info_frame.pack(fill=tk.X, padx=10, pady=5)

                label_frame = ttk.Frame(info_frame)
                label_frame.pack(fill=tk.X)

                ttk.Label(label_frame, text=f"{icon} {field}:",
                         font=("Arial", 10, "bold")).pack(side=tk.LEFT)

                value_label = ttk.Label(label_frame, text=str(value),
                                       font=("Arial", 10))
                value_label.pack(side=tk.LEFT, padx=(10, 0))

                # 添加分隔线
                if i < len(basic_info_fields) - 1:
                    ttk.Separator(basic_scrollable_frame, orient='horizontal').pack(fill=tk.X, padx=10, pady=2)

        basic_canvas.pack(side="left", fill="both", expand=True)
        basic_scrollbar.pack(side="right", fill="y")

        # 图片信息标签页
        if info.get("主图链接"):
            image_frame = ttk.Frame(notebook)
            notebook.add(image_frame, text="🖼️ 商品图片")

            # 图片链接列表
            img_text_frame = ttk.Frame(image_frame)
            img_text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            ttk.Label(img_text_frame, text="📷 商品图片链接:",
                     font=("Arial", 12, "bold")).pack(anchor=tk.W, pady=(0, 10))

            img_text = tk.Text(img_text_frame, wrap=tk.WORD, height=15)
            img_scrollbar = ttk.Scrollbar(img_text_frame, orient=tk.VERTICAL, command=img_text.yview)
            img_text.configure(yscrollcommand=img_scrollbar.set)

            # 添加图片链接
            for i, img_url in enumerate(info["主图链接"], 1):
                img_text.insert(tk.END, f"{i}. {img_url}\n\n")

            img_text.config(state=tk.DISABLED)
            img_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            img_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(15, 0))

        # 复制商品ID按钮
        if info.get('商品ID'):
            def copy_id():
                detail_window.clipboard_clear()
                detail_window.clipboard_append(info['商品ID'])
                messagebox.showinfo("复制成功", f"商品ID已复制到剪贴板: {info['商品ID']}")

            ttk.Button(button_frame, text="📋 复制商品ID",
                      command=copy_id).pack(side=tk.LEFT, padx=(0, 10))

        # 关闭按钮
        ttk.Button(button_frame, text="❌ 关闭",
                  command=detail_window.destroy).pack(side=tk.RIGHT)

    def handle_clear_cache(self):
        """清理缓存"""
        try:
            if messagebox.askyesno("确认", "确定要清理所有缓存数据吗？此操作不可恢复！"):
                cache_files = ["./cache/ids.json"]

                for file_path in cache_files:
                    if os.path.exists(file_path):
                        os.remove(file_path)

                self.main_window.log_message("缓存已清理", "SUCCESS")
                messagebox.showinfo("完成", "缓存已清理")

                # 刷新数据
                self.handle_refresh_stats()
                self.handle_refresh_data()

        except Exception as e:
            self.main_window.log_message(f"清理缓存失败: {str(e)}", "ERROR")
            messagebox.showerror("错误", f"清理缓存失败: {str(e)}")

    def handle_show_statistics(self):
        """显示详细统计信息"""
        try:
            config = self.get_config()
            stats = DataProcessor.get_data_statistics(config["result_path"], config["cache_path"])

            # 数据一致性状态
            consistency_text = "正常" if stats['data_consistency'] else "异常 (缓存与文件数量不匹配)"
            consistency_color = "✓" if stats['data_consistency'] else "⚠"

            stats_text = f"""
📊 拼多多商品数据统计报告

📁 文件统计:
   • 结果文件数量: {stats['result_files']} 个
   • 缓存商品数量: {stats['cached_products']} 个
   • 数据一致性: {consistency_color} {consistency_text}

💾 存储统计:
   • 总文件大小: {stats['total_size_mb']} MB
   • 平均文件大小: {stats['avg_file_size_kb']} KB

⏰ 时间统计:
   • 最新文件时间: {stats['latest_file_time']}
   • 最旧文件时间: {stats['oldest_file_time']}
   • 统计更新时间: {stats['last_update']}

📂 路径信息:
   • 结果目录: {config['result_path']}
   • 缓存文件: {config['cache_path']}
            """

            messagebox.showinfo("详细统计信息", stats_text)

        except Exception as e:
            self.main_window.log_message(f"获取统计信息失败: {str(e)}", "ERROR")
            messagebox.showerror("错误", f"获取统计信息失败: {str(e)}")

    def handle_toggle_browser(self):
        """处理浏览器显示/隐藏切换"""
        try:
            if not self.spider:
                self.main_window.log_message("爬虫未初始化，请先登录", "WARNING")
                return

            # 切换浏览器显示状态
            visible = self.spider.toggle_browser_visibility()

            # 更新按钮状态
            self.main_window.update_browser_button(visible)

        except Exception as e:
            self.main_window.log_message(f"切换浏览器显示失败: {str(e)}", "ERROR")

    def handle_manual_verify(self):
        """处理手动验证"""
        try:
            if not self.spider:
                self.main_window.log_message("爬虫未初始化，请先登录", "WARNING")
                return

            # 显示浏览器进行手动验证
            success = self.spider.show_browser_for_manual_verify()

            if success:
                # 更新按钮状态
                self.main_window.update_browser_button(True)

                # 显示操作提示
                result = messagebox.askquestion(
                    "手动验证",
                    "浏览器已显示，请手动完成验证操作（如滑块验证）。\n\n完成后点击'是'继续，点击'否'取消。",
                    icon='question'
                )

                if result == 'yes':
                    self.main_window.log_message("手动验证完成，继续执行", "SUCCESS")
                else:
                    self.main_window.log_message("用户取消手动验证", "INFO")

        except Exception as e:
            self.main_window.log_message(f"手动验证失败: {str(e)}", "ERROR")

    def on_closing(self):
        """程序关闭时的处理"""
        try:
            # 停止采集
            if self.main_window.is_collecting and self.spider:
                self.spider.stop_collect()

            # 关闭浏览器
            if self.spider:
                self.spider.close()

            # 保存设置
            self.main_window.save_settings()

            # 关闭窗口
            self.main_window.root.destroy()

        except Exception as e:
            print(f"关闭程序时出错: {str(e)}")
            self.main_window.root.destroy()

    def run(self):
        """运行应用程序"""
        try:
            self.main_window.run()
        except KeyboardInterrupt:
            self.on_closing()
        except Exception as e:
            messagebox.showerror("错误", f"程序运行出错: {str(e)}")


def main():
    """主函数"""
    try:
        app = PddSpiderApp()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
