"""
登录对话框
"""
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time


class LoginDialog:
    def __init__(self, parent, spider):
        """
        初始化登录对话框
        
        Args:
            parent: 父窗口
            spider: 爬虫实例
        """
        self.parent = parent
        self.spider = spider
        self.result = False
        
        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("账号登录")
        self.dialog.geometry("520x650")  # 增加宽度和高度以容纳更多内容
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.center_window()
        
        # 创建界面
        self.create_widgets()
        

        
        # 状态变量
        self.current_step = "phone"  # phone, code, login
        self.phone_number = ""
        self.verification_code = ""
        self.login_thread = None
        
    def center_window(self):
        """窗口居中"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (520 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (650 // 2)
        self.dialog.geometry(f"520x650+{x}+{y}")
        
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(main_frame, text="拼多多账号登录", font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))

        # 域名选择框架
        self.domain_frame = ttk.LabelFrame(main_frame, text="选择登录域名", padding=10)
        self.domain_frame.pack(fill=tk.X, pady=5)

        self.domain_var = tk.StringVar(value="mobile.pinduoduo.com")
        domain_options = [
            ("mobile.pinduoduo.com", "mobile.pinduoduo.com"),
            ("mobile.yangkeduo.com", "mobile.yangkeduo.com")
        ]

        for text, value in domain_options:
            ttk.Radiobutton(self.domain_frame, text=text, variable=self.domain_var,
                           value=value).pack(side=tk.LEFT, padx=10)

        # 手机号输入框架
        self.phone_frame = ttk.LabelFrame(main_frame, text="第一步：输入手机号", padding=10)
        self.phone_frame.pack(fill=tk.X, pady=5)

        self.phone_entry = ttk.Entry(self.phone_frame, font=("Arial", 14), width=25)
        self.phone_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        self.phone_entry.bind('<KeyRelease>', self.on_phone_change)

        self.phone_btn = ttk.Button(self.phone_frame, text="获取验证码", command=self.send_verification_code, state=tk.DISABLED)
        self.phone_btn.pack(side=tk.RIGHT)

        # 验证码输入框架（初始隐藏）
        # 先不创建，等需要时再创建
        
        # 状态显示
        self.status_var = tk.StringVar(value="请输入11位手机号码")
        status_label = ttk.Label(main_frame, textvariable=self.status_var, font=("Arial", 10))
        status_label.pack(pady=10)

        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.pack(fill=tk.X, pady=5)

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=20)

        self.cancel_btn = ttk.Button(button_frame, text="取消", command=self.cancel_login)
        self.cancel_btn.pack(side=tk.RIGHT, padx=5)
        
        # 说明文本
        info_text = """
登录说明：
1. 点击"开始登录"按钮开始登录流程
2. 在手机号输入框中输入11位手机号码
3. 点击"确认"后系统会发送验证码
4. 在验证码输入框中输入收到的6位验证码
5. 点击"确认"完成登录
        """
        
        info_label = ttk.Label(main_frame, text=info_text, font=("Arial", 9), 
                              foreground="gray", justify=tk.LEFT)
        info_label.pack(pady=10)
        
    def on_phone_change(self, event=None):
        """手机号输入变化时的处理"""
        phone = self.phone_entry.get().strip()
        if len(phone) == 11 and phone.isdigit():
            self.phone_btn.config(state=tk.NORMAL)
            self.status_var.set("手机号格式正确，点击获取验证码")
        else:
            self.phone_btn.config(state=tk.DISABLED)
            if len(phone) > 0:
                self.status_var.set("请输入正确的11位手机号码")
            else:
                self.status_var.set("请输入11位手机号码")

    def on_code_change(self, event=None):
        """验证码输入变化时的处理"""
        if hasattr(self, 'code_entry') and hasattr(self, 'code_btn'):
            code = self.code_entry.get().strip()
            if len(code) == 6 and code.isdigit():
                self.code_btn.config(state=tk.NORMAL)
                self.status_var.set("验证码格式正确，点击确认登录")
            else:
                self.code_btn.config(state=tk.DISABLED)
                if len(code) > 0:
                    self.status_var.set("请输入正确的6位验证码")
                else:
                    self.status_var.set("请输入收到的6位验证码")

    def send_verification_code(self):
        """发送验证码"""
        phone = self.phone_entry.get().strip()
        if len(phone) != 11 or not phone.isdigit():
            messagebox.showerror("错误", "请输入正确的11位手机号码")
            return

        self.phone_number = phone
        self.phone_btn.config(state=tk.DISABLED)
        self.phone_entry.config(state=tk.DISABLED)
        self.progress.start()
        self.status_var.set("正在发送验证码...")

        # 在新线程中执行发送验证码
        self.login_thread = threading.Thread(target=self.send_code_process)
        self.login_thread.daemon = True
        self.login_thread.start()

    def send_code_process(self):
        """发送验证码的处理流程"""
        try:
            # 获取选择的域名
            selected_domain = self.domain_var.get()

            # 调用爬虫开始登录流程，传递域名参数
            success = self.spider.start_login_process(self.phone_number, selected_domain)

            # 在主线程中更新UI
            self.dialog.after(0, self.code_sent_callback, success)

        except Exception as e:
            self.dialog.after(0, self.code_send_error, str(e))

    def code_sent_callback(self, success):
        """验证码发送完成回调"""
        print(f"DEBUG: code_sent_callback called with success={success}")  # 调试信息
        self.progress.stop()
        self.progress.pack_forget()  # 隐藏进度条

        if success:
            print("DEBUG: 创建并显示验证码输入框")  # 调试信息

            # 创建验证码输入框架
            main_frame = self.dialog.winfo_children()[0]  # 获取主框架
            self.code_frame = ttk.LabelFrame(main_frame, text="第二步：输入验证码", padding=10)
            self.code_frame.pack(fill=tk.X, pady=5, after=self.phone_frame)

            # 创建验证码输入组件
            self.code_entry = ttk.Entry(self.code_frame, font=("Arial", 14), width=25)
            self.code_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
            self.code_entry.bind('<KeyRelease>', self.on_code_change)

            # 创建确认按钮
            self.code_btn = ttk.Button(self.code_frame, text="确认登录",
                                      command=self.confirm_login, state=tk.DISABLED)
            self.code_btn.pack(side=tk.RIGHT)

            # 强制更新布局
            self.dialog.update_idletasks()

            # 确保输入框可见并获得焦点
            self.code_entry.focus_set()

            self.status_var.set("验证码已发送，请输入收到的6位验证码")
            self.current_step = "code"
            print("DEBUG: 验证码输入框已显示")  # 调试信息
        else:
            print("DEBUG: 验证码发送失败")  # 调试信息
            # 重置状态
            self.phone_btn.config(state=tk.NORMAL)
            self.phone_entry.config(state=tk.NORMAL)
            self.status_var.set("验证码发送失败，请重试")
            messagebox.showerror("失败", "验证码发送失败，请检查手机号或重试")

    def code_send_error(self, error_msg):
        """验证码发送错误"""
        self.progress.stop()
        self.progress.pack_forget()  # 隐藏进度条
        self.phone_btn.config(state=tk.NORMAL)
        self.phone_entry.config(state=tk.NORMAL)
        self.status_var.set("验证码发送出错")
        messagebox.showerror("错误", f"验证码发送过程出错：{error_msg}")

    def confirm_login(self):
        """确认登录"""
        if not hasattr(self, 'code_entry'):
            messagebox.showerror("错误", "验证码输入框未初始化")
            return

        code = self.code_entry.get().strip()
        if len(code) != 6 or not code.isdigit():
            messagebox.showerror("错误", "请输入正确的6位验证码")
            return

        self.verification_code = code
        self.code_btn.config(state=tk.DISABLED)
        self.code_entry.config(state=tk.DISABLED)
        self.progress.pack(fill=tk.X, pady=5)  # 重新显示进度条
        self.progress.start()
        self.status_var.set("正在验证登录...")

        # 在新线程中执行登录验证
        self.login_thread = threading.Thread(target=self.login_verify_process)
        self.login_thread.daemon = True
        self.login_thread.start()

    def login_verify_process(self):
        """登录验证处理流程"""
        try:
            # 调用爬虫完成登录
            success = self.spider.complete_login_process(self.verification_code)

            # 在主线程中更新UI
            self.dialog.after(0, self.login_completed, success)

        except Exception as e:
            self.dialog.after(0, self.login_error, str(e))
        

        
    def login_completed(self, success):
        """登录完成"""
        self.progress.stop()
        self.progress.pack_forget()  # 隐藏进度条

        if success:
            self.status_var.set("登录成功！")
            self.result = True
            messagebox.showinfo("成功", "登录成功！")
            self.dialog.after(1000, self.dialog.destroy)
        else:
            self.status_var.set("登录失败")
            if hasattr(self, 'code_btn'):
                self.code_btn.config(state=tk.NORMAL)
            if hasattr(self, 'code_entry'):
                self.code_entry.config(state=tk.NORMAL)
            messagebox.showerror("失败", "登录失败，请重试")

    def login_error(self, error_msg):
        """登录错误"""
        self.progress.stop()
        self.progress.pack_forget()  # 隐藏进度条
        self.status_var.set("登录出错")
        if hasattr(self, 'code_btn'):
            self.code_btn.config(state=tk.NORMAL)
        if hasattr(self, 'code_entry'):
            self.code_entry.config(state=tk.NORMAL)
        messagebox.showerror("错误", f"登录过程出错：{error_msg}")
        
    def cancel_login(self):
        """取消登录"""
        if self.login_thread and self.login_thread.is_alive():
            # 停止爬虫操作
            if hasattr(self.spider, 'should_stop'):
                self.spider.should_stop = True
                
        self.result = False
        self.dialog.destroy()
        
    def show(self):
        """显示对话框并返回结果"""
        self.dialog.wait_window()
        return self.result


def show_login_dialog(parent, spider):
    """
    显示登录对话框
    
    Args:
        parent: 父窗口
        spider: 爬虫实例
        
    Returns:
        bool: 登录是否成功
    """
    dialog = LoginDialog(parent, spider)
    return dialog.show()
