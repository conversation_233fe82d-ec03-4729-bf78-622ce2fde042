# -*- mode: python ; coding: utf-8 -*-
import os

block_cipher = None

# 检查Chrome目录是否存在
chrome_data = [('Chrome', 'Chrome')] if os.path.exists('Chrome') else []

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=chrome_data,
    hiddenimports=[
        # DrissionPage相关
        'DrissionPage',
        'DrissionPage.common',
        'DrissionPage.common.web',
        'DrissionPage.common.session', 
        'DrissionPage.common.chromium',
        'DrissionPage.common.settings',
        'DrissionPage.common.configs',
        'DrissionPage.common.locator',
        'DrissionPage.common.actions',
        'DrissionPage.common.keys',
        'DrissionPage.common.by',
        
        # GUI相关
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.filedialog',
        'tkinter.scrolledtext',
        'tkinter.constants',
        
        # 网络和数据处理
        'requests',
        'requests.adapters',
        'requests.auth',
        'requests.cookies',
        'requests.models',
        'requests.sessions',
        'urllib3',
        'urllib3.poolmanager',
        'urllib3.connectionpool',
        
        # 标准库
        'json',
        'threading',
        'datetime',
        'pathlib',
        'subprocess',
        'shutil',
        'os',
        'sys',
        're',
        'time',
        'random',
        'queue',
        'collections',
        'functools',
        'itertools',
        'typing',
        
        # 其他可能需要的模块
        'openpyxl',
        'pandas',
        'csv',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'numpy.testing',
        'scipy',
        'IPython',
        'jupyter',
        'notebook',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 收集DrissionPage的所有子模块
from PyInstaller.utils.hooks import collect_submodules, collect_data_files
drission_submodules = collect_submodules('DrissionPage')
drission_data = collect_data_files('DrissionPage')

a.hiddenimports.extend(drission_submodules)
a.datas.extend(drission_data)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='PDD_Spider',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
