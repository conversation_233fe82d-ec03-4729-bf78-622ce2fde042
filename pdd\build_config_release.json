{"name": "拼多多采集工具", "build_type": "release", "main_script": "main.py", "include_chrome": true, "include_license": true, "hidden_imports": ["DrissionPage", "DrissionPage.common", "tkinter", "tkinter.ttk", "tkinter.messagebox", "tkinter.filedialog", "requests", "json", "threading", "queue", "datetime", "<PERSON><PERSON><PERSON>", "base64", "license_system", "license_system.license_manager", "license_system.license_interface", "license_system.crypto_utils", "license_system.hardware_info", "license_system.ui_integration", "license_system.config", "license_system.exceptions", "cryptography", "cryptography.fernet", "cryptography.hazmat.primitives"]}