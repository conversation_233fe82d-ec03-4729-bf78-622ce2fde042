"""
卡密系统配置
"""
import os

# 卡密系统版本
LICENSE_VERSION = "1.0.0"

# 加密配置
ENCRYPTION_KEY = b"PDD_SPIDER_LICENSE_KEY_2024_SECURE"  # 32字节密钥
RSA_KEY_SIZE = 2048

# 卡密格式配置
LICENSE_FORMAT = "XXXX-XXXX-XXXX-XXXX-XXXX"
LICENSE_SEGMENTS = 5
SEGMENT_LENGTH = 4

# 功能权限定义
FEATURES = {
    "basic": "基础功能",
    "batch_collect": "批量采集",
    "export_data": "数据导出", 
    "advanced_filter": "高级筛选",
    "auto_retry": "自动重试",
    "multi_thread": "多线程采集",
    "schedule": "定时采集",
    "api_access": "API接口"
}

# 授权类型
LICENSE_TYPES = {
    "trial": {
        "name": "试用版",
        "duration_days": 7,
        "features": ["basic", "batch_collect"]
    },
    "personal": {
        "name": "个人版", 
        "duration_days": 365,
        "features": ["basic", "batch_collect", "export_data", "advanced_filter"]
    },
    "professional": {
        "name": "专业版",
        "duration_days": 365, 
        "features": ["basic", "batch_collect", "export_data", "advanced_filter", 
                    "auto_retry", "multi_thread"]
    },
    "enterprise": {
        "name": "企业版",
        "duration_days": 365,
        "features": list(FEATURES.keys())  # 全功能
    }
}

# 文件路径
LICENSE_FILE = os.path.join(os.path.expanduser("~"), ".pdd_license")
HARDWARE_FILE = os.path.join(os.path.expanduser("~"), ".pdd_hardware")

# RSA公钥 (用于验证卡密签名)
PUBLIC_KEY = """-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA2K8VhIvQxJ7BQcQN9X2L
kM3pR4vN8wE6tY9sF2hK1nM7oP3qR8sT5vW2xZ4cN1mL6jK9pQ8rS7uV0wX3yE5z
A9bC2dF4gH6iJ8kL0mN5oP7qR9sT2vW8xZ1cN4mL3jK6pQ5rS9uV7wX0yE2zA8bC
5dF1gH9iJ5kL7mN2oP4qR6sT8vW5xZ8cN7mL0jK3pQ2rS6uV4wX7yE8zA5bC8dF7
gH2iJ2kL4mN8oP1qR3sT5vW8xZ5cN0mL6jK9pQ8rS7uV0wX3yE5zA9bC2dF4gH6i
J8kL0mN5oP7qR9sT2vW8xZ1cN4mL3jK6pQ5rS9uV7wX0yE2zA8bC5dF1gH9iJ5kL
7mN2oP4qR6sT8vW5xZ8cN7mL0jK3pQ2rS6uV4wX7yE8zA5bC8dF7gH2iJ2kL4mN8
QIDAQAB
-----END PUBLIC KEY-----"""

# 试用模式配置
TRIAL_MODE = {
    "enabled": True,
    "duration_days": 7,
    "features": ["basic", "batch_collect"],
    "daily_limit": 100  # 每日采集限制
}

# 验证配置
VERIFICATION = {
    "startup_check": True,      # 启动时验证
    "periodic_check": True,     # 定期验证
    "check_interval": 3600,     # 验证间隔(秒)
    "hardware_binding": True,   # 硬件绑定
    "time_check": True         # 时间验证
}
