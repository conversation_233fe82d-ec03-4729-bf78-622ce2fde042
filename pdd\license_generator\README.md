# 🔑 卡密生成器

拼多多采集工具的卡密生成和管理工具。

## 📋 功能特性

### 🔑 卡密生成
- **单个生成** - 为特定客户生成个性化卡密
- **批量生成** - 一次生成多个卡密，支持自定义前缀
- **多种授权类型** - 试用版、个人版、专业版、企业版
- **功能权限控制** - 精确控制每个卡密的功能权限
- **硬件绑定** - 可选的硬件绑定防止盗用

### 👥 客户管理
- **客户信息管理** - 姓名、联系方式、邮箱、备注
- **客户关联** - 卡密与客户关联，便于管理
- **客户编辑** - 支持编辑和删除客户信息

### 🎫 卡密管理
- **卡密列表** - 查看所有生成的卡密
- **状态管理** - 激活、过期、禁用状态管理
- **详情查看** - 查看卡密的详细信息
- **批量操作** - 复制、禁用、导出等操作

### 🛠️ 实用工具
- **卡密验证** - 验证卡密的有效性和详细信息
- **硬件信息** - 获取当前设备的硬件指纹
- **数据库管理** - 导入导出数据库，清理过期卡密
- **操作日志** - 记录所有操作历史

## 🚀 快速开始

### 1. 环境要求
- Python 3.7+
- tkinter (通常随Python安装)
- sqlite3 (Python内置)
- license_system 模块

### 2. 启动方式

#### Windows用户
```bash
# 双击运行
start_generator.bat

# 或命令行运行
python start_generator.py
```

#### Linux/Mac用户
```bash
python3 start_generator.py
```

### 3. 首次使用
1. 启动程序后会自动创建数据库
2. 在"客户管理"标签页添加客户信息
3. 在"生成卡密"标签页生成卡密
4. 在"卡密管理"标签页查看和管理卡密

## 📖 使用指南

### 🔑 生成卡密

#### 单个生成
1. 选择或新建客户
2. 选择授权类型（会自动设置功能权限）
3. 设置有效期和用户ID
4. 可选择启用硬件绑定
5. 点击"生成卡密"

#### 批量生成
1. 点击"批量生成"按钮
2. 设置生成数量和用户ID前缀
3. 选择授权类型和有效期
4. 点击"开始生成"
5. 生成完成后可保存到文件

### 👥 客户管理
- **新建客户**: 点击"新建客户"按钮
- **编辑客户**: 选中客户后点击"编辑客户"
- **删除客户**: 选中客户后点击"删除客户"（会同时删除相关卡密）

### 🎫 卡密管理
- **复制卡密**: 选中卡密后点击"复制卡密"
- **查看详情**: 选中卡密后点击"查看详情"
- **禁用卡密**: 选中卡密后点击"禁用卡密"
- **导出列表**: 点击"导出列表"保存为CSV文件

### 🛠️ 工具功能
- **验证卡密**: 在工具标签页输入卡密进行验证
- **硬件信息**: 查看当前设备的硬件指纹
- **数据库管理**: 导入导出数据库文件

## 🏷️ 授权类型

| 类型 | 有效期 | 功能权限 | 适用场景 |
|------|--------|----------|----------|
| **试用版** | 7天 | 基础功能、批量采集 | 新用户试用 |
| **个人版** | 1年 | 基础、批量、导出、筛选 | 个人用户 |
| **专业版** | 1年 | 个人版+重试+多线程 | 专业用户 |
| **企业版** | 1年 | 全功能 | 企业用户 |

## 🔧 功能权限

- `basic` - 基础功能
- `batch_collect` - 批量采集
- `export_data` - 数据导出
- `advanced_filter` - 高级筛选
- `auto_retry` - 自动重试
- `multi_thread` - 多线程采集
- `schedule` - 定时采集
- `api_access` - API接口

## 📁 文件结构

```
license_generator/
├── license_generator.py    # 主程序
├── dialogs.py             # 对话框类
├── start_generator.py     # 启动脚本
├── start_generator.bat    # Windows启动脚本
├── README.md             # 说明文档
└── license_data.db       # 数据库文件（自动创建）
```

## 🔐 安全特性

### 卡密安全
- **AES-256加密** - 卡密内容使用强加密
- **数字签名** - 防止卡密被篡改
- **硬件绑定** - 可选的设备绑定
- **时间验证** - 自动过期检查

### 数据安全
- **本地存储** - 数据存储在本地SQLite数据库
- **备份恢复** - 支持数据库导入导出
- **操作日志** - 记录所有关键操作

## 🚨 注意事项

1. **数据备份** - 定期备份license_data.db文件
2. **权限管理** - 妥善保管生成器，避免未授权使用
3. **卡密分发** - 安全地将卡密分发给客户
4. **过期管理** - 定期清理过期卡密
5. **硬件绑定** - 硬件绑定后无法在其他设备使用

## 🔧 故障排除

### 常见问题

#### 1. 程序无法启动
```
错误：无法导入卡密系统模块
```
**解决方案：** 确保license_system目录存在且完整

#### 2. 数据库错误
```
数据库文件损坏
```
**解决方案：** 删除license_data.db文件，程序会自动重新创建

#### 3. 卡密生成失败
```
生成卡密失败
```
**解决方案：** 检查输入参数是否正确，确保功能权限至少选择一个

### 技术支持

如遇到其他问题，请检查：
1. Python版本是否为3.7+
2. license_system模块是否完整
3. 数据库文件是否有读写权限
4. 操作日志中的错误信息

## 📈 版本历史

### v1.0.0
- ✅ 基础卡密生成功能
- ✅ 客户管理功能
- ✅ 卡密管理功能
- ✅ 批量生成功能
- ✅ 工具集成
- ✅ 数据库管理

---

**开发者**: PDD Spider Team  
**版本**: v1.0.0  
**更新时间**: 2024-07-31
