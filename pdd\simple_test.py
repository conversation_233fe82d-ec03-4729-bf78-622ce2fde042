#!/usr/bin/env python3
import sys
import os
import traceback

sys.path.insert(0, '.')

def test_basic_encryption():
    """测试基础加密功能"""
    try:
        print("开始测试基础加密...")
        
        import license_system.crypto_utils as crypto
        
        # 测试简单加密解密
        test_data = "Hello World"
        encrypted = crypto.CryptoUtils.encrypt_data(test_data)
        decrypted = crypto.CryptoUtils.decrypt_data(encrypted)
        
        print(f"原始数据: {test_data}")
        print(f"加密后: {encrypted[:50]}...")
        print(f"解密后: {decrypted}")
        print(f"一致性: {'✅' if test_data == decrypted else '❌'}")
        
        return test_data == decrypted
        
    except Exception as e:
        print(f"基础加密测试失败: {e}")
        traceback.print_exc()
        return False

def test_license_generation():
    """测试卡密生成"""
    try:
        print("\n开始测试卡密生成...")
        
        import license_system.crypto_utils as crypto
        
        # 生成卡密
        license_key = crypto.LicenseGenerator.generate_license(
            user_id="TEST001",
            license_type="personal",
            duration_days=30,
            features=["basic"],
            hardware_fingerprint=None
        )
        
        print(f"生成的卡密: {license_key}")
        print(f"卡密长度: {len(license_key)}")
        print(f"连字符数量: {license_key.count('-')}")
        
        # 检查格式
        segments = license_key.split('-')
        print(f"段数: {len(segments)}")
        for i, seg in enumerate(segments):
            print(f"段{i+1}: '{seg}' (长度: {len(seg)})")
        
        return license_key
        
    except Exception as e:
        print(f"卡密生成测试失败: {e}")
        traceback.print_exc()
        return None

def test_license_decoding(license_key):
    """测试卡密解码"""
    try:
        print(f"\n开始测试卡密解码...")
        print(f"待解码卡密: {license_key}")
        
        import license_system.crypto_utils as crypto
        
        # 解码卡密
        decoded = crypto.CryptoUtils.decode_license_data(license_key)
        
        print("解码成功！")
        print("解码后的数据:")
        for key, value in decoded.items():
            print(f"  {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"卡密解码测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    print("🧪 卡密系统测试")
    print("=" * 50)
    
    # 测试基础加密
    basic_ok = test_basic_encryption()
    
    # 测试卡密生成
    license_key = test_license_generation()
    
    # 测试卡密解码
    decode_ok = False
    if license_key:
        decode_ok = test_license_decoding(license_key)
    
    print("\n📊 测试结果:")
    print(f"基础加密: {'✅' if basic_ok else '❌'}")
    print(f"卡密生成: {'✅' if license_key else '❌'}")
    print(f"卡密解码: {'✅' if decode_ok else '❌'}")
    
    if basic_ok and license_key and decode_ok:
        print("\n🎉 所有测试通过！")
    else:
        print("\n❌ 部分测试失败")

if __name__ == "__main__":
    main()
