#!/usr/bin/env python3
"""
卡密生成器启动脚本
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def check_dependencies():
    """检查依赖"""
    missing_deps = []
    
    try:
        import tkinter
    except ImportError:
        missing_deps.append("tkinter")
    
    try:
        import sqlite3
    except ImportError:
        missing_deps.append("sqlite3")
    
    # 检查卡密系统
    try:
        from license_system.crypto_utils import LicenseGenerator
        from license_system.config import LICENSE_TYPES
        from license_system.hardware_info import HardwareInfo
    except ImportError as e:
        print(f"❌ 错误：无法导入卡密系统模块")
        print(f"详细错误: {e}")
        print("请确保license_system目录存在且完整")
        return False
    
    if missing_deps:
        print(f"❌ 缺少依赖: {', '.join(missing_deps)}")
        return False
    
    return True

def main():
    """主函数"""
    print("🔑 拼多多采集工具 - 卡密生成器")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，无法启动")
        input("按回车键退出...")
        return
    
    print("✅ 依赖检查通过")
    
    # 启动生成器
    try:
        from license_generator import LicenseGeneratorGUI
        
        print("🚀 启动卡密生成器...")
        app = LicenseGeneratorGUI()
        app.run()
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")

if __name__ == "__main__":
    main()
