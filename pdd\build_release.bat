@echo off
chcp 65001 >nul
title 构建发布版本

echo.
echo 🚀 拼多多采集工具 - 发布版本构建
echo ================================================

cd /d "%~dp0"

echo 📍 当前目录: %CD%
echo.

REM 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python
    echo 请确保已安装Python并添加到PATH环境变量
    echo.
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

REM 构建发布版本
echo 🔨 开始构建发布版本...
echo.

python auto_spider.py --type release --version-config release

if errorlevel 1 (
    echo.
    echo ❌ 构建失败
    pause
) else (
    echo.
    echo ✅ 构建完成
    echo 📁 可执行文件位置: dist\
    echo.
    pause
)

exit /b 0
