# 拼多多商品数据采集工具 GUI版

## 项目概述

这是一个现代化的拼多多商品数据采集工具，提供了友好的图形用户界面(GUI)。该工具基于DrissionPage进行浏览器自动化操作，支持账号登录、批量采集、数据管理和导出等功能。

## 主要功能

### 🔐 账号管理
- **可视化登录**：图形界面登录，支持手机验证码
- **登录状态检查**：自动检测登录状态
- **会话保持**：自动保存和恢复登录信息

### 📊 数据采集
- **批量采集**：设置页数，自动采集多页商品数据
- **单品采集**：输入商品ID进行精确采集
- **实时进度**：可视化进度条和日志显示
- **智能去重**：自动跳过已采集的商品

### 📁 数据管理
- **数据预览**：表格形式查看已采集数据
- **多格式导出**：支持CSV、Excel、JSON格式导出
- **数据统计**：实时显示采集统计信息
- **数据删除**：选择性删除不需要的数据

### ⚙️ 系统设置
- **参数配置**：采集延时、重试次数等可调节
- **路径设置**：自定义数据保存和导出路径
- **运行模式**：支持有头/无头浏览器模式

### 🛡️ 防风控机制
- **随机延时**：智能延时策略避免检测
- **状态监控**：实时检测账号状态
- **自动重试**：失败自动重试机制

## 系统要求

- **操作系统**：Windows 10/11, macOS 10.14+, Linux
- **Python版本**：Python 3.7+
- **内存要求**：至少2GB RAM
- **磁盘空间**：至少500MB可用空间

## 项目结构

```
pdd/
├── main.py              # 主程序入口
├── run.py               # 开发启动脚本
├── build.py             # 打包脚本
├── build.bat            # Windows打包批处理
├── requirements.txt     # 依赖包列表
├── config.json          # 配置文件
├── gui/                 # GUI界面模块
│   ├── __init__.py
│   ├── main_window.py   # 主窗口
│   └── login_dialog.py  # 登录对话框
├── core/                # 核心功能模块
│   ├── __init__.py
│   ├── spider.py        # 爬虫核心类
│   └── data_processor.py # 数据处理类
├── utils/               # 工具模块
│   └── __init__.py
├── cache/               # 缓存目录
│   └── ids.json         # 已采集商品ID缓存
├── result/              # 采集结果目录
│   └── [商品ID].json    # 商品数据文件
└── export/              # 导出文件目录
```

## 快速开始

### 方法一：直接运行可执行文件（推荐）

1. **下载发布版本**
   - 从Releases页面下载最新版本的压缩包
   - 解压到任意目录

2. **运行程序**
   - 双击 `拼多多采集工具.exe` 启动程序
   - 或运行 `install.bat` 安装到系统

### 方法二：从源码运行

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd pdd
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **运行程序**
   ```bash
   python main.py
   # 或者
   python run.py
   ```

## 使用指南

### 🚀 首次使用

1. **启动程序**
   - 双击可执行文件或运行Python脚本

2. **账号登录**
   - 切换到"账号登录"标签页
   - 点击"开始登录"按钮
   - 在弹出对话框中输入手机号
   - 输入收到的验证码
   - 等待登录成功提示

3. **配置设置**
   - 切换到"系统设置"标签页
   - 根据需要调整采集参数
   - 设置数据保存路径
   - 点击"保存设置"

### 📊 数据采集

#### 批量采集
1. 切换到"数据采集"标签页
2. 选择"批量采集"模式
3. 设置要采集的页数（建议从小数量开始测试）
4. 点击"开始采集"
5. 在日志区域查看实时进度

#### 单品采集
1. 选择"单品采集"模式
2. 输入商品ID（从商品链接中获取）
3. 可选输入商品名称
4. 点击"开始采集"

### 📁 数据管理

1. **查看数据**
   - 切换到"数据管理"标签页
   - 点击"刷新数据"查看最新采集结果

2. **导出数据**
   - 选择导出格式（CSV/Excel/JSON）
   - 点击"选择导出路径"设置保存位置
   - 点击"导出数据"开始导出

3. **数据清理**
   - 选中不需要的数据行
   - 点击"删除选中"进行清理

## 打包为可执行文件

### 自动打包（推荐）

**Windows用户：**
```bash
# 双击运行
build.bat
```

**其他系统：**
```bash
python build.py
```

### 手动打包

1. **安装PyInstaller**
   ```bash
   pip install pyinstaller
   ```

2. **执行打包命令**
   ```bash
   pyinstaller --onefile --windowed --name=拼多多采集工具 main.py
   ```

3. **查看结果**
   - 可执行文件位于 `dist/` 目录
   - 可以独立运行，无需Python环境

## 配置说明

### 系统配置文件 (config.json)

```json
{
  "headless": true,           // 无头模式运行
  "delay_min": 3,            // 最小延时（秒）
  "delay_max": 5,            // 最大延时（秒）
  "retry_times": 3,          // 重试次数
  "result_path": "./result/", // 结果保存路径
  "export_path": "./export/", // 导出文件路径
  "cookies_path": "./cookies.json",
  "url_path": "./url.txt",
  "cache_path": "./cache/ids.json"，
   "last_successful_port": 9237 //上次端口

}
```

### 界面设置

- **无头模式**：推荐开启，提高性能
- **采集延时**：建议3-5秒，避免被检测
- **重试次数**：网络不稳定时增加重试次数
- **保存路径**：可自定义数据存储位置

## 数据格式

### 原始数据 (JSON)
每个商品的完整数据保存为单独的JSON文件，包含：
- 商品基本信息（ID、名称、价格、销量等）
- 商品图片（主图、详情图链接）
- 店铺信息（店铺ID、名称等）
- 商品规格（可选规格、库存等）
- 详细描述和参数

### 导出数据格式
支持导出为以下格式：
- **CSV**：适合Excel打开，便于数据分析
- **Excel**：原生Excel格式，支持多工作表
- **JSON**：结构化数据，便于程序处理

导出的数据包含提取的关键字段：
- 商品ID、商品名称、价格
- 店铺名称、分类ID、销量提示
- 品牌ID、主图链接、采集时间等

## 常见问题

### ❓ 登录相关

**Q: 登录时提示"需要重新登录"**
A: 可能是登录信息过期，点击"清除登录信息"后重新登录

**Q: 收不到验证码**
A: 检查手机号是否正确，或选择语音验证码

**Q: 登录后采集失败**
A: 可能遇到滑块验证，程序会自动显示浏览器窗口，手动完成验证即可

### ❓ 采集相关

**Q: 采集速度很慢**
A: 这是正常现象，为了避免被检测，程序会随机延时

**Q: 采集中断了怎么办**
A: 程序会自动保存进度，重新开始采集时会跳过已采集的商品

**Q: 提示"账号被风控"**
A: 暂停采集，等待一段时间后重新登录，或更换账号

### ❓ 数据相关

**Q: 导出的数据不完整**
A: 检查result目录下的JSON文件是否完整，可能是采集过程中出现错误

**Q: 如何查看原始数据**
A: 原始数据保存在result目录下，每个商品一个JSON文件

## 技术支持

### 🐛 问题反馈

如遇到问题，请提供以下信息：
1. 操作系统版本
2. Python版本（如果从源码运行）
3. 错误截图或日志
4. 复现步骤

### 📝 日志查看

程序运行日志可以通过以下方式查看：
1. GUI界面的日志区域
2. 点击"保存日志"按钮保存到文件
3. 控制台输出（从源码运行时）

### 🔧 故障排除

**程序无法启动：**
1. 检查是否有杀毒软件拦截
2. 确认系统权限是否足够
3. 尝试以管理员身份运行

**采集无数据：**
1. 检查网络连接
2. 确认登录状态
3. 查看日志中的错误信息

**浏览器相关错误：**
1. 确保Chrome浏览器已安装
2. 检查Chrome版本是否过旧
3. 尝试重启程序

## 更新日志

### v1.0.0 (2024-01-XX)
- ✨ 全新GUI界面设计
- 🔐 可视化登录流程
- 📊 实时数据预览和统计
- 📁 多格式数据导出
- ⚙️ 可配置系统设置
- 🛡️ 增强的防风控机制
- 📦 一键打包为可执行文件

## 开发计划

- [ ] 支持代理IP轮换
- [ ] 添加数据分析功能
- [ ] 支持定时任务
- [ ] 多账号管理
- [ ] 数据库存储支持
- [ ] Web管理界面

## 贡献指南

欢迎提交Issue和Pull Request！

1. Fork本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开Pull Request

## 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 免责声明

⚠️ **重要提醒**

本工具仅供学习和研究使用，请遵守以下原则：

1. **法律合规**：遵守相关法律法规和网站使用条款
2. **个人使用**：仅限个人研究，不得用于商业用途
3. **数据保护**：妥善保管采集的数据，不得泄露或滥用
4. **频率控制**：合理控制采集频率，避免对服务器造成压力
5. **责任自负**：使用本工具产生的任何后果由使用者自行承担

**请在使用前仔细阅读并同意以上条款。**